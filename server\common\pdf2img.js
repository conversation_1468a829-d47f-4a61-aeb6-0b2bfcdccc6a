const {pdf} = require('pdf-to-img');
const fs = require('fs');
const path = require('path');

// console.log('pdf', pdf);

async function convertPDFToImages(pdfPath, outputDir = 'tmp/') {
  const filename = path.basename(pdfPath);
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Convert PDF to images
  const images = await pdf(pdfPath.replace(/^\//, ''), { scale: 3 });
  let page = 1;
  let outImgs = []
  for await (const image of images) {
    outImgs.push(`${filename}_${page}.png`)
    await fs.writeFileSync(outputDir+`${filename}_${page}.png`, image);
    page++;
  }

  return outImgs;
}

module.exports = { convertPDFToImages };