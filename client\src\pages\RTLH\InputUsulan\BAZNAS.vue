<template>
  <Modal
    title="PROPOSAL"
    :show.sync="xshow"
    :showActions="!disabled"
    width="900px"
    :style="'overflow-x:hidden;'"
    @onSubmit="Save"
  >
    <div style="font-size: 14px; width: 600px; max-width: 100vw">
      <div style="width: 100%">
        <div style="padding: 10px; background: #f3f3f3">
          &nbsp;&nbsp;DOKUMEN
        </div>
        <div style="padding: 20px">
          <CheckUpload
            label="Surat Rekomendasi"
            accept=".pdf"
            :value.sync="forms.PermohonanBansos"
          />
          <CheckUpload
            label="Nota Belanja"
            accept=".pdf"
            :value.sync="forms.BAPokmas"
          />
        </div>
      </div>
      <div style="width: 100%">
        <div style="padding: 10px; background: #f3f3f3">
          <i class="fa fa-globe"></i>&nbsp;&nbsp;PETA LOKASI
          <span style="color: red">{{
            !forms.GeoLat ? '(Belum Diisi)' : ''
          }}</span>
        </div>
        <div id="MapDiv" style="position: relative; width: 100%">
          <Map
            ref="map"
            :lat.sync="forms.GeoLat"
            :lon.sync="forms.GeoLng"
            style="
              width: 600px;
              max-width: 100vw;
              height: 340px;
              display: block;
            "
          />
        </div>
      </div>
      <div>
        <div style="display: flex; flex-wrap: wrap">
          <!-- <Uploader
            label="RENCANA KEG."
            :value.sync="forms.PermohonanBansos"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="BA. MUSDES"
            :value.sync="forms.BAPokmas"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="DFTR HADIR"
            :value.sync="forms.SKPokmas"
            accept=".pdf"
          ></Uploader>
          <Uploader
            label="SP KEPDES"
            :value.sync="forms.PaktaIntegritas"
            accept=".pdf"
          ></Uploader> -->
          <Uploader
            label="KTP"
            :value.sync="forms.KTP"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="RUMAH DEPAN"
            :value.sync="forms.RumahDepan"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="RMH SAMPING"
            :value.sync="forms.RumahSamping"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="JAMBAN"
            :value.sync="forms.Jamban0"
            v-if="forms.JenisKloset !== 4"
            style="margin-top: 0"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <div v-else style="width: 150px; height: 150px; text-align: center">
            <v-icon large style="margin-top: 35%">mdi-close-thick</v-icon>
          </div>
          <Uploader
            label="ATAP 0%"
            :value.sync="forms.Atap0"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="LANTAI 0%"
            :value.sync="forms.Lantai0"
            accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
          ></Uploader>
        </div>
      </div>
      <div
        class="iblock dv-kerusakan"
        style="width: 600px; overflow: hidden; padding: 10px"
      >
        <div style="padding: 0px; font-weight: bold">USULAN PERBAIKAN</div>
        <div style="flex-wrap: wrap; width: 100%" class="usulan-perbaikan">
          <div
            style="padding: 5px; width: 50%; min-width: 280px"
            class="form-inline"
          >
            <Checkbox
              label="Atap"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.AtapTdkLayak"
            />
            <Checkbox
              label="Lantai"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.LantaiTdkLayak"
            />
            <Checkbox
              label="Dinding"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.DindingTdkLayak"
            />
            <Checkbox
              label="Jendela"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.CahayaTdkLayak"
            />
            <!-- <Checkbox
              label="Pintu"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              :value.sync="forms.CahayaTdkLayak"
            /> -->
            <Checkbox
              label="Km. Mandi/Jamban"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.JambanTdkLayak"
            />
          </div>
          <div
            style="padding: 5px; width: 50%; min-width: 280px"
            class="form-inline"
          >
            <Checkbox
              label="Listrik"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.ListrikTdkLayak"
            />
            <Checkbox
              label="Pondasi"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.PondasiTdkLayak"
            />
            <Checkbox
              label="Sloof"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.SloofTdkLayak"
            />
            <Checkbox
              label="Kolom"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.KolomTdkLayak"
            />
            <Checkbox
              label="Balok"
              style="width: 200px"
              checkedIcon="mdi-home-remove"
              checkedColor="error"
              :value.sync="forms.BalokTdkLayak"
            />
          </div>
        </div>
      </div>
      <div style="padding: 10px; background: orange; display: flex">
        <div style="padding: 9px; font-weight: bold" v-if="rab.Swadaya">
          Kisaran Swadaya: Rp. {{ rab.Swadaya | format }}
        </div>
        <div style="padding: 9px; font-weight: bold; color: white" v-else>
          RAB BELUM DIMASUKKAN
        </div>
        <v-spacer />
        <v-btn text color="primary" @click="OpenRAB">
          ESTIMASI RAB
          <v-icon right> mdi-chevron-right </v-icon>
        </v-btn>
      </div>
    </div>
    <RAB
      :show.sync="showRAB"
      :NIK="forms.NIK"
      :tahun="tahun"
      :filters="rabFilters"
    />
  </Modal>
</template>
<script>
import { mapGetters } from 'vuex'
import RAB from './RAB.vue'
import CheckUpload from '../../Backlog/Pencairan/CheckUpload.vue'
export default {
  components: {
    RAB,
    CheckUpload,
  },
  data: () => ({
    xshow: false,
    showRAB: false,
    forms: {},
    rab: {},
  }),
  props: {
    show: Boolean,
    disabled: Boolean,
    nik: [String, Number],
    tahun: [String, Number],
  },
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    rabFilters() {
      let f = ['E']
      if (this.forms.AtapTdkLayak) f.push('B')
      if (this.forms.LantaiTdkLayak) f.push('C')
      if (this.forms.DindingTdkLayak) f.push('A')
      if (this.forms.JambanTdkLayak) f.push('D')
      // console.log(f)
      return f
    },
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
  },
  methods: {
    async populate() {
      this.loading = true
      if (this.nik) {
        if (this.$refs.map) this.$refs.map.clearMarkers()
        let { data } = await this.$api.call('PRM.SelPersonProposal', {
          NIK: this.nik,
        })
        // Object.assign(this.forms, data[0])
        this.forms = data[0]

        let rabData = await this.$api.call('PRM.SelRAB', {
          NIK: this.nik,
          Tahun: this.tahun,
        })
        if (rabData.data.length) this.rab = rabData.data[0]
        else this.rab = {}
      } else {
        this.forms = {}
      }
      this.loading = false
    },
    OpenRAB() {
      if (this.rabFilters.length < 2) {
        alert('Pastikan USULAN PERBAIKAN sudah sesuai, sebelum membuat RAB')
      }
      this.showRAB = true
    },
    async Save() {
      this.error = ''
      let ret = await this.$api.call('PRM.SavPersonProposal', {
        ...this.forms,
        GeoLoc: this.forms.GeoLat
          ? this.forms.GeoLat + '|' + this.forms.GeoLng
          : null,
      })
      if (ret.success) {
        this.xshow = false
        this.$emit('save')
      } else this.error = ret.message
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
    overflow-x: hidden;
  }
  .usulan-perbaikan {
    display: flex;
  }
}
.is-mobile {
  .modal-proposal {
    .ui-upload {
      width: 50%;
      .imgbox {
        width: 100%;
      }
    }
    .usulan-perbaikan {
      display: block;
    }
  }
}
</style>
