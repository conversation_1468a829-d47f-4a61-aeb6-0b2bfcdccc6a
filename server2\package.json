{"name": "fastify-server", "version": "1.0.0", "description": "Node.js server using Fastify framework", "main": "server.js", "scripts": {"start": "nodemon server.js", "dev": "nodemon server.js"}, "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "@fastify/cookie": "^9.2.0", "jsonwebtoken": "^9.0.2", "mysql": "^2.18.1", "moment": "^2.29.4", "dotenv": "^16.3.1", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["fastify", "nodejs", "api"], "author": "", "license": "ISC"}