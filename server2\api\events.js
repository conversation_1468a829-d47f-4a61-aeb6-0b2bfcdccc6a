// Fastify-compatible Server-Sent Events handler
const eventsModule = {
  clients: {},

  // Fastify route handler for SSE
  handler: async (request, reply) => {
    // Check if response is already sent
    if (reply.sent) return

    let { clientId } = request.params

    // Set SSE headers
    reply.raw.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    const data = `data: from server\n\n`;
    reply.raw.write(data);

    const cId = (request.body?._userId || 'anonymous') + ':' + Date.now();

    const newClient = {
      id: cId,
      userId: request.body?._userId,
      roleId: request.body?._roleId,
      reply: reply.raw // Store the raw response object
    };

    // console.log(`${cId} Connected`);
    eventsModule.clients[cId] = newClient;

    // Handle client disconnect
    reply.raw.on('close', () => {
      // console.log(`${cId} Connection closed`);
      delete eventsModule.clients[cId]
    });

    // Handle request abort
    request.raw.on('aborted', () => {
      // console.log(`${cId} Request aborted`);
      delete eventsModule.clients[cId]
    });

    // Keep connection alive - don't call reply.send()
    // The connection will stay open for SSE
  },

  // Notification function remains the same
  notify: (data, opt = { type: 'notification' }) => {
    for (let clientId in eventsModule.clients) {
      let client = eventsModule.clients[clientId]
      // for now send only for this role
      if (opt.type == 'command' || [1, 2].includes(client.roleId)) {
        if (!opt.targetUserId || (opt.targetUserId && client.userId == opt.targetUserId)) {
          try {
            client.reply.write(`event: ${opt.type}\ndata: ${JSON.stringify(data)}\n\n`)
          } catch (err) {
            // Client might have disconnected, remove from clients
            delete eventsModule.clients[clientId]
          }
        }
      }
    }
  },

  // Fastify plugin for SSE routes
  plugin: async (fastify, options) => {
    // Register SSE route
    fastify.get('/events/:clientId?', eventsModule.handler)

    // Decorate fastify instance with notify function
    fastify.decorate('notifyClients', eventsModule.notify)

    // Add clients getter
    fastify.decorate('getSSEClients', () => eventsModule.clients)
  }
}

module.exports = eventsModule