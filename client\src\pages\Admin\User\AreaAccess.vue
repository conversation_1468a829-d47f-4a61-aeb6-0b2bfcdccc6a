<template>
  <div>
    <Grid
      :datagrid.sync="areaAccess"
      dbref="Arch.UserArea"
      :dbparams="dbparams"
      :disabled="true"
      style="height: 400px"
      class="dense"
      :columns="[
        {
          name: 'Area',
          value: 'AreaName',
          width: '220px',
        },
        {
          name: 'Aks<PERSON>',
          value: 'AllowAccess',
        },
      ]"
    >
      <template v-slot:row-AllowAccess="{ row }">
        <div center>
          <Checkbox :value.sync="row.AllowAccess" />
        </div>
      </template>
    </Grid>
  </div>
</template>
<script>
export default {
  data: () => ({
    areaAccess: null,
    forms: {},
  }),
  props: {
    userId: [String, Number],
    'page-data': Array,
  },
  async mounted() {},
  watch: {
    areaAccess(val) {
      this.$emit('update:page-data', val)
    },
  },
  computed: {
    dbparams() {
      return {
        UserID: this.userId,
      }
    },
  },
  methods: {
    ShowRoleAccess() {
      this.roleAccess = true
    },
  },
}
</script>
