<template>
  <v-menu
    ref="menu"
    v-model="showPopup"
    :close-on-content-click="false"
    :return-value.sync="time"
    transition="scale-transition"
    offset-y
    max-width="290px"
    min-width="290px"
  >
    <template v-slot:activator="{ on }">
      <XInput type="text" :label="label" :value.sync="vmodel" v-on="on" />
    </template>
    <v-time-picker
      v-if="showPopup"
      v-model="time"
      full-width
      @click:minute="$refs.menu.save(time)"
    ></v-time-picker>
  </v-menu>
</template>
<script>
export default {
  data: () => ({
    showPopup: false,
    vmodel: null
  }),
  props: {
    label: String,
    value: [String, Object]
  }
};
</script>
