<template>
  <div>
    <div style="display: flex">
      <XSelect
        dbref="PRM.SelProposal"
        :value.sync="proposal"
        :valueAsObject="true"
        width="80px"
        style="margin-right: 10px"
      />
      <Checkbox
        :value.sync="hasAllocation"
        style="margin-left: 10px; margin-top: 2px"
        text="Mempunyai Alokasi"
      />
      <!-- <Checkbox
        :value.sync="showAllPhase"
        style="margin-left: 10px; margin-top: 2px"
        text="Se<PERSON><PERSON>"
      /> -->
    </div>
    <Grid
      dbref="PRM.AlokasiSkGub"
      :dbparams="params"
      :datagrid.sync="forms.XmlAlokasi"
      :filter="filterGrid"
      :disabled="true"
      :autopaging="false"
      :doRebind="rebind"
      height="calc(100vh - 240px)"
      :columns="[
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          width: '150px',
        },
        ...phases
          .map((i) => [
            {
              name: 'Tahap ' + i,
              value: 'Tahap' + i,
              class: 'plain phase-' + i,
            },
            {
              name: '',
              value: 'DesaTahap' + i,
              class: 'right phase-' + i,
              tooltip: 'Jumlah Alokasi Desa',
            },
          ])
          .flat(),
        {
          name: 'K. AKhir',
          value: 'KuotaAkhir',
          class: 'plain',
        },
        {
          name: 'Sisa',
          value: 'Sisa',
          class: 'right --lightyellow',
        },
        {
          name: 'Total',
          value: 'Total',
          class: 'right',
        },
        {
          name: '',
          value: 'Desa',
          class: 'plain',
        },
      ]"
    >
      <template v-for="idx in phases" #[`row-Tahap${idx}`]="{ row }">
        <XInput
          :key="'input' + idx"
          type="number"
          :value.sync="row['Tahap' + idx]"
          width="80px"
          style="margin-left: 5px"
        />
      </template>
      <template v-slot:row-KuotaAkhir="{ row }">
        <XInput
          type="number"
          :value.sync="row.KuotaAkhir"
          width="80px"
          style="margin-left: 5px"
        />
      </template>
      <template v-slot:row-Desa="{ row }">
        <v-btn x-small text color="primary" @click="OpenDesa(row)">
          DESA
          <v-icon x-small right>mdi-open-in-new</v-icon>
        </v-btn>
      </template>
      <template v-slot:row-Total="{ row }">
        <div style="text-align: right; font-weight: bold">
          {{
            row.KuotaAkhir ||
            (parseInt(row.Tahap1 || 0) +
              parseInt(row.Tahap2 || 0) +
              parseInt(row.Tahap3 || 0) +
              parseInt(row.Tahap4 || 0) +
              parseInt(row.Tahap5 || 0) +
              parseInt(row.Tahap6 || 0))
              | format
          }}
        </div>
      </template>
      <template v-slot:footer="{}">
        <tr style="font-weight: bold">
          <td style="padding: 8px 12px">TOTAL</td>
          <template v-for="idx in phases">
            <!-- eslint-disable-next-line -->
            <td :key="'td' + idx" style="padding: 8px 12px; text-align: right">
              {{ total['tahap' + idx] | format }}
            </td>
            <td :key="'td2' + idx"></td>
          </template>
          <td style="padding: 8px 12px; text-align: right">
            {{ total.akhir | format }}
          </td>
          <td
            style="
              padding: 8px 12px;
              text-align: right;
              background: lightyellow;
            "
          >
            {{ total.sisa | format }}
          </td>
          <td style="padding: 8px 12px; text-align: right">
            {{ total.Grand | format }}
          </td>
        </tr>
      </template>
    </Grid>
    <br />
    <v-btn :color="saveColor" @click="Save"> SIMPAN </v-btn>
    <ModalDesa
      :show.sync="showDesa"
      :params="selected"
      @close="DesaClosed($event)"
    />
  </div>
</template>
<script>
import ModalDesa from './ModalDesa.vue'
export default {
  components: { ModalDesa },
  data: () => ({
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: {
      XmlAlokasi: [],
    },
    saveColor: 'primary',
    hasAllocation: false,
    showAllPhase: false,
    phases: [1, 2, 3, 4, 5, 6],
    showDesa: false,
    selected: {},
    rebind: 1,
    // total: {},
  }),
  computed: {
    params() {
      let { InputName } = this.proposal
      return { Tahun: InputName }
    },
    total() {
      let tahap1 = 0
      let tahap2 = 0
      let tahap3 = 0
      let tahap4 = 0
      let tahap5 = 0
      let tahap6 = 0
      let akhir = 0
      let sisa = 0
      for (let d of this.forms.XmlAlokasi) {
        tahap1 += parseInt(d.Tahap1) || 0
        tahap2 += parseInt(d.Tahap2) || 0
        tahap3 += parseInt(d.Tahap3) || 0
        tahap4 += parseInt(d.Tahap4) || 0
        tahap5 += parseInt(d.Tahap5) || 0
        tahap6 += parseInt(d.Tahap6) || 0
        akhir += parseInt(d.KuotaAkhir) || 0
        sisa += parseInt(d.Sisa) || 0
      }
      let summary = {
        tahap1,
        tahap2,
        tahap3,
        tahap4,
        tahap5,
        tahap6,
        sisa,
        akhir: akhir,
        Grand: akhir || tahap1 + tahap2 + tahap3 + tahap4 + tahap5 + tahap6,
      }
      let max = 0
      for (let i = 0; i < 6; i++) if (summary[`tahap${i}`] > max) max = i
      summary.maxPhase = max
      return summary
    },
  },
  methods: {
    async Save() {
      await this.$api.call('PRM_SavAlokasiSkGub', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
      this.saveColor = 'primary'
      // this.rebind++
    },
    filterGrid(row) {
      return this.hasAllocation
        ? Boolean(
            row.Tahap1 ||
              row.Tahap2 ||
              row.Tahap3 ||
              row.Tahap4 ||
              row.Tahap5 ||
              row.Tahap6
          )
        : true
    },
    OpenDesa(row) {
      this.selected = { ...row, Tahun: this.proposal.InputName, Sumber: 2 }
      this.showDesa = true
    },
    DesaClosed(alokasi) {
      let row = this.forms.XmlAlokasi.find(
        (d) => d.Kabupaten == this.selected.Kabupaten
      )
      row.Tahap1 = alokasi.tahap1
      row.Tahap2 = alokasi.tahap2
      row.Tahap3 = alokasi.tahap3
      row.Tahap4 = alokasi.tahap4
      row.Tahap5 = alokasi.tahap5
      row.Tahap6 = alokasi.tahap6
      // this.saveColor = 'warning'
      this.Save()
    },
  },
}
</script>
<style lang="scss">
.--lightyellow {
  background: lightyellow;
}
</style>
