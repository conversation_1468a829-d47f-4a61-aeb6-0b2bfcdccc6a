<template>
  <div
    :id="targetId + '_popover'"
    class="ui-popover"
    :class="{ show: isShow || show }"
    @click="handleClick"
    v-click-outside="handleClickOutside"
  >
    <slot> ... </slot>
  </div>
</template>
<script>
export default {
  data: () => ({
    isShow: false,
  }),
  props: {
    targetId: String,
    on: String,
    show: Boolean,
    posmode: String,
    position: {
      type: String,
      default: 'bottom',
    },
  },
  watch: {
    isShow() {
      this.$emit('update:show', this.isShow)
    },
  },
  mounted() {
    if (this.targetId) {
      let $el = document.getElementById(this.targetId)
      if ($el) $el.addEventListener(this.on, this.handleShow)
    }
  },
  methods: {
    handleShow(evt) {
      let $this = document.getElementById(this.targetId + '_popover')
      if (this.on == 'contextmenu') {
        let pops = document.querySelectorAll('.ui-popover.show')
        for (let p of pops) {
          if (p !== $this) p.classList.remove('show')
        }
        $this.style.top = evt.layerY + 'px'
        $this.style.left = evt.layerX + 'px'
        evt.preventDefault()
      } else if (this.posmode === 'absolute') {
        let pos = evt.target.getBoundingClientRect()
        if (pos.top > window.innerHeight * 0.6) {
          $this.style.bottom = window.innerHeight - pos.top + 'px'
          $this.style.left = pos.left + 'px'
          $this.style.top = 'auto'
        } else {
          $this.style.top = pos.top + pos.height + 'px'
          $this.style.left = pos.left + 'px'
          $this.style.bottom = 'auto'
        }
      }

      this.isShow = true
      this.$emit('onShow', $this)
      this.$emit('click', $this)
    },
    handleClick(evt) {
      let $this = document.getElementById(this.id + '_popover')
      if (
        evt.target &&
        (evt.target.closest('.popover-close') ||
          evt.target.classList.contains('popover-close'))
      ) {
        this.isShow = false
        this.$emit('onHide', $this)
      }
      this.$emit('click', $this)
    },
    handleClickOutside(evt) {
      let $this = document.getElementById(this.id + '_popover')
      if (
        evt.target.getAttribute('id') == this.targetId ||
        evt.target.closest('#' + this.targetId)
      ) {
        // this.isShow = true;
      } else {
        this.isShow = false
        this.$emit('onHide', $this)
      }
    },
  },
}
</script>
<style lang="scss">
.ui-popover {
  position: absolute;
  background: white;
  z-index: 100;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.3);
  display: none;
}
.ui-popover.show {
  display: initial !important;
}
</style>
