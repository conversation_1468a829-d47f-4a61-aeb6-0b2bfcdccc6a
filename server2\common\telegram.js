const {Telegraf, <PERSON><PERSON>} = require('telegraf')
const fs = require('fs')
const db = require('./db')
const events = require('../api/events');


// const bot = new Telegraf('6202031893:AAHUsWd1xN5-WX-5SLm7FKf8UzyGs9344TQ')
const bot = new Telegraf('6779220015:AAH036cTQvOI9aQ9D6QP9KqDkk3JHpvxfPw')
const ADMIN_ID = '662216470'

const session = {
  set: (ctx, k, v) => {
    if(!session.params[ctx.chat.id]) session.params[ctx.chat.id] = {}
    if (typeof k == 'object') {
      session.params[ctx.chat.id] = {...session.params[ctx.chat.id], ...k}
    } else {
      session.params[ctx.chat.id][k] = v
    }
  },
  get:(ctx, k) => {
    if (!session.params[ctx.chat.id]) return null
    return session.params[ctx.chat.id][k]
  },
  params: {},
  isAdmin: (ctx) => {
    return ctx.chat.id == ADMIN_ID
  }
}

bot.command('start', ctx => {
  ctx.reply('Terimakasih anda telah mendaftar di Telegram Simperum')
})

bot.telegram.setMyCommands([
  {
    command: 'start',
    description: 'Mendaftarkan akun anda ke Simperum',
  },
  {
    command: 'cekrekom',
    description: 'Cek rekom yang berjalan',
  },
  {
    command: 'cek',
    description: 'cek [NIK]',
  },
  {
    command: 'useronline',
    description: 'Jumlah user yang online',
  },
  {
    command: 'bukarekom',
    description: '[ADMIN] buka rekom',
  },
  {
    command: 'logs',
    description: '[ADMIN] tampilkan log',
  }
]);

bot.command('useronline', async ctx => {
  // let msg = ctx.message.text
  let reply = ''
  let len = 0
  for (let key in events.clients) {
    reply += key + '\n'
    len++;
  }
  bot.telegram.sendMessage(ctx.chat.id, `Jumlah user yg terkoneksi adalah ${len}:\n\n` + reply, {})
})

bot.command('cekrekom', async ctx => {
  const res = await db.query('SELECT Tahun, Rekom FROM prm_rekom WHERE IsClosed = 0')
  if (res.length > 0) {
    bot.telegram.sendMessage(ctx.chat.id, `Rekom yang berjalan untuk tahun ${res[0].Tahun} adalah ${res[0].Rekom}`, {})
  } else {
    bot.telegram.sendMessage(ctx.chat.id, `Tidak ada rekom yang sedang berjalan`, {})
  }
})

bot.command('logs', async ctx => {
  let msg = ctx.message.text
  if (ctx.chat.id == ADMIN_ID) {
    const logs = fs.readFileSync('/app/tmp/backend.log', 'utf-8')
    bot.telegram.sendMessage(ctx.chat.id, logs, {})
  } else {
    bot.telegram.sendMessage(ctx.chat.id, 'Perintah ini hanya untuk admin', {})
  }
})

const ISSUES = {
  'buka_rekom': {
    prompt:(ctx) => {
      let msg = ctx.message.text
      let params = msg.match(/(\d{1,2})/)
      if(params) {
        const rekom = params[1]
        ISSUES['buka_rekom'].callback(ctx, rekom)
      } else {
        ctx.reply(`Masukkan angka REKOM:`)
      }
    },
    callback: async (ctx, rekom) => {
      if (!ctx) return
      await db.query(`UPDATE prm_rekom SET Rekom = 'REKOM ${rekom}' WHERE IsClosed = 0 AND Tahun = YEAR(NOW())`)
      ctx.reply(`REKOM ${rekom} telah dibuka`)
    }
  },
  'cek_nik': {
    prompt:(ctx) => {
      let msg = ctx.message.text
      let params = msg.match(/(\d{16})/)
      if(params) {
        const NIK = params[1]
        ISSUES['cek_nik'].callback(ctx, NIK)
      } else {
        ctx.reply(`Masukkan NIK:`)
      }
    },
    callback: async (ctx, NIK) => {
      if (!ctx) return
      ctx.sendChatAction('typing', ctx.chat.id)
      let res = await db.query(`SELECT 
          p.NoRef, p.Sumber, p.Tahun, p.NIK, p.Nama, p.Kabupaten, p.Kecamatan, p.Kelurahan, p.KodeWilayah, p.ScoreTag, p.MampuSwadaya,
          d.Tahun ProposalTahun, CASE WHEN d.ApprovalDate IS NOT NULL THEN 'Disetujui' ELSE 'Belum Disetujui' END ProposalStatus,
          d.Sumber ProposalSumber, d.Tahun ProposalTahun,
          b.Tahun BansosTahun, b.Tahapan, 
          CASE WHEN d.NIK IS NULL AND d2.NIK IS NOT NULL THEN 'nik_proposal_beda'
              WHEN b.NIK IS NULL AND b2.NIK IS NOT NULL THEN 'nik_bansos_beda'
              WHEN COALESCE(d.NIK,d2.NIK) IS NOT NULL AND COALESCE(b.Tahapan, b2.Tahapan) IS NULL THEN 'need_cleanup' 
              ELSE NULL END Issues 
        FROM prm_pbdt p
          LEFT JOIN prm_proposaldet d
          ON p.NIK = d.NIK 
          LEFT JOIN prm_bansos b
          ON p.NIK = b.NIK 
          LEFT JOIN prm_proposaldet d2
          ON p.NoRef = d2.NoPBDT 
          LEFT JOIN prm_bansos b2
          ON p.NoRef = b2.NoPBDT 
        WHERE p.NIK =  ${NIK}`)
      if (res.length > 0) {
        let d = res[0]
        session.set(ctx, {NIK: NIK, Tahun: d.ProposalTahun})
        ctx.reply(`Nama: ${d.Nama}\n` +
          `Lokasi: ${d.Kabupaten},${d.Kecamatan}, ${d.Kelurahan}\n` +
          `Skor: ${d.ScoreTag || '-'} ; Swadaya: ${d.MampuSwadaya}\n` +
          `Proposal: ${d.ProposalTahun || '-'}[${d.ProposalSumber || '-'}] ${d.ProposalStatus || '-'}\n` +
          `Bansos: ${d.BansosTahun || '-'} ${d.Tahapan || '-'}` +
          `${d.Issues ? `\nMASALAH: ${d.Issues}` : ''}`, {})
        if (session.isAdmin(ctx)) {
          if (d.ProposalStatus == 'Disetujui' && !d.BansosTahun) {
            ctx.app.act = 'missing_bansos'
            ISSUES['missing_bansos'].prompt(ctx)
          } else if (ISSUES[d.Issues]) {
            ctx.app.act = d.Issues
            ISSUES[d.Issues].prompt(ctx)
          }
        }
      } else {
        ctx.reply(`Data tidak ditemukan`, {})
      }
    }
  },
  'missing_bansos': {
    prompt: (ctx) => {
      ctx.reply(
        "Data disetujui, namun tidak masuk bansos, perbaiki data?",
        Markup.inlineKeyboard([
          Markup.button.callback("Batal", "cancel"),
          Markup.button.callback("Ya", "ok"),
        ]),
      )
    },
    callback: (ctx) => {
      bot.action('missing_bansos_ok', async (ctx) => {
        ctx.editMessageReplyMarkup();
      })
    }
  },
  'nik_proposal_beda': {
    prompt: (ctx) => {
      ctx.reply(
        "NIK proposal berbeda dengan NIK PBDT, perbaiki?",
        Markup.inlineKeyboard([
          Markup.button.callback("Batal", "cancel"),
          Markup.button.callback("Ya", "nik_proposal_beda_ok"),
        ]),
      )
    },
    callback: () => {
      bot.action('nik_proposal_beda_ok', async (ctx) => {
        ctx.editMessageReplyMarkup();
        let p = session.params[ctx.chat.id]
        let res = await db.query(`
          update prm_pbdt p
          join prm_proposaldet d
          ON p.NoRef = d.NoPBDT 
          SET d.NIK = p.NIK
          WHERE p.NIK = ${p.NIK}
          AND d.NIK <> p.NIK`)
        
        if (res.affectedRows > 0)
          ctx.reply(`Berhasil memperbaiki ${res.affectedRows} data`)
        else
          ctx.reply(`Data tidak ditemukan`)
      })
    }
  },
  'need_cleanup': {
    prompt: (ctx) => {
      ctx.reply(
        "Data Masuk proposal namun tidak masuk bansos, HAPUS proposal?",
        Markup.inlineKeyboard([
          Markup.button.callback("Batal", "cancel"),
          Markup.button.callback("Ya", "need_cleanup_ok"),
        ]),
      )
    },
    callback: () => {
      bot.action('need_cleanup_ok', async (ctx) => {
        ctx.editMessageReplyMarkup();
        let p = session.params[ctx.chat.id]
        let res = await db.query(`
          DELETE d FROM prm_proposaldet d
            LEFT JOIN prm_bansos b
            ON d.NIK = b.NIK
          WHERE d.NIK = ${p.NIK} AND d.Tahun = ${p.Tahun} AND b.NIK IS NULL`)
        
        if (res.affectedRows > 0)
          ctx.reply(`Berhasil menghapus ${res.affectedRows} data`)
        else
          ctx.reply(`Data tidak ditemukan`)
      })
    }
  }
}
// register ISSUES
for (let k in ISSUES) {
  ISSUES[k].callback()
}

bot.command('bukarekom', async ctx => {
  let msg = ctx.message.text
  if (ctx.chat.id == ADMIN_ID) {
    if (!ctx.app) ctx.app = {}
    session.set(ctx, 'cmd', 'buka_rekom')
    let rekom = msg.match(/bukarekom (\d{1,2})/)
    if (rekom) {
      ISSUES['buka_rekom'].callback(ctx, rekom[1])
    } else {
      ctx.reply(`Masukkan angka REKOM:`)
    }
  } else {
    ctx.reply('Perintah ini hanya untuk admin')
  }
})

bot.command('cek', ctx => {
  if (!ctx.app) ctx.app = {}
  session.set(ctx, 'cmd', 'cek_nik')
  let msg = ctx.message.text
  let params = msg.match(/cek (\d{16})/)
  if (params) {
    const NIK = params[1]
    ISSUES['cek_nik'].callback(ctx, NIK)
  } else {
    ctx.reply(`masukkan NIK:`)
  }
})

bot.command('onetime', (ctx) =>
  ctx.reply(
    "Keyboard wrap",
    Markup.inlineKeyboard([
      Markup.button.callback("Batal", "cancel"),
      Markup.button.callback("Ya", "ok"),
    ]),
  )
)

bot.action('cancel', (ctx) => {
  ctx.editMessageReplyMarkup();
  ctx.reply('Canceled')
})

bot.action('ok', (ctx) => {
  ctx.editMessageReplyMarkup();
  ctx.reply('[Not Implemented]')
})

bot.on('text', (ctx) => {
  const cmd = session.get(ctx, 'cmd')
  if(!cmd) ctx.reply('Perintah tidak dikenali')
  else if (ISSUES[cmd]) ISSUES[cmd].prompt(ctx) 
})

bot.launch()
console.log('Telegram Bot Active')

// Enable graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'))
process.once('SIGTERM', () => bot.stop('SIGTERM'))

module.exports = {
  send: (id, text) => {
    if (text)
      bot.telegram.sendMessage(id, text, {})
  },
  sendAdmin: (text) => {
    if (text)
      bot.telegram.sendMessage(ADMIN_ID, text, {})
  }
}
