<template>
  <div
    @click="isClicked = true"
    style="letter-spacing: 1.1px"
    :style="{ cursor: isClicked ? '' : 'pointer' }"
  >
    {{
      isClicked ? nik : nik?.replace(/^(.{3})(.{11})/, '$1 * * * * * * * * * ')
    }}
  </div>
</template>
<script>
export default {
  data: () => ({
    isClicked: false,
  }),
  props: {
    nik: String,
  },
  watch: {
    nik() {
      this.isClicked = false
    },
  },
}
</script>
<style lang="scss"></style>
