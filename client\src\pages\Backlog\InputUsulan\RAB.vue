<template>
  <Modal
    title="INPUT RAB"
    :show.sync="xshow"
    width="400px"
    @onSubmit="Save"
    :disabled="disabled"
  >
    <div class="form-inline" style="display: flex"></div>
    <Grid
      :datagrid="datagrid"
      :key="rebind"
      @before-save="SaveGrid"
      :editMode="['edit']"
      :columns="[
        {
          name: '',
          value: 'KodeRAB',
        },
        {
          name: 'Material',
          value: 'JenisMaterial',
          editable: {
            com: 'XInput',
            width: '450px',
          },
        },
        {
          name: 'Satuan',
          value: 'Satuan',
          editable: {
            com: 'XInput',
            width: '100px',
          },
        },
        {
          name: 'Volume',
          value: 'Volume',
          editable: {
            com: 'XInput',
            width: '100px',
          },
        },
        {
          name: 'Harga',
          value: 'Harga',
          editable: {
            com: 'XInput',
            width: '110px',
          },
        },
        {
          name: 'Total',
          value: 'Total',
        },
      ]"
    >
      <template v-slot:row-KodeRAB="{ row }">
        <div :class="'ordr-' + row.KodeRAB ? row.KodeRAB.length : 1">
          {{ row.KodeRAB }}
        </div>
      </template>
      <template v-slot:row-JenisMaterial="{ row }">
        <div :class="'ordr-' + row.KodeRAB.length" style="display: flex">
          <div
            :style="
              row.JenisMaterial == 'PEKERJAAN DINDING' ||
              row.JenisMaterial == 'PEKERJAAN ATAP'
                ? 'margin-top:4px;'
                : ''
            "
          >
            {{ row.JenisMaterial }}
          </div>
          <v-spacer />
          <XSelect
            v-if="row.JenisMaterial == 'PEKERJAAN DINDING'"
            style="width: 170px; font-weight: normal"
            :items="pilihanDinding"
            :value.sync="dinding"
          />
          <XSelect
            v-if="row.JenisMaterial == 'PEKERJAAN ATAP'"
            style="width: 170px; font-weight: normal"
            :items="pilihanAtap"
            :value.sync="atap"
          />
        </div>
      </template>
      <template v-slot:row-Volume="{ row }">
        <div
          v-if="row.KodeRAB ? row.KodeRAB.length : 1 > 1"
          style="text-align: right"
        >
          <!-- {{ parseInt(row.Volume) || '' }} -->
          <XInput
            type="number"
            :value.sync="row.Volume"
            style="width: 100px"
            @change="CalcRow(row)"
          />
        </div>
      </template>
      <template v-slot:row-Harga="{ row }">
        <div v-if="parseInt(row.Volume)">
          <XInput
            type="number"
            :value.sync="row.Harga"
            style="width: 100px"
            @change="CalcRow(row)"
          />
        </div>
      </template>
      <template v-slot:row-Total="{ row }">
        <div
          :class="'ordr-' + row.KodeRAB.length"
          style="text-align: right"
          v-if="row.Total != null"
        >
          Rp. {{ row.Total | format }}
        </div>
      </template>
    </Grid>
    <div
      style="
        display: flex;
        font-weight: bold;
        font-size: 12px;
        padding: 8px 12px;
      "
    >
      <div>TOTAL</div>
      <v-spacer />
      <div style="padding-right: 50px">Rp. {{ grandTotal | format }}</div>
    </div>
    <!-- <div
      style="display:flex; font-weight:bold; background: orange; font-size: 12px; padding: 8px 12px;"
    >
      <div>
        KISARAN SWADAYA
      </div>
      <v-spacer />
      <div style="padding-right:50px;">Rp. {{ swadaya | format }}</div>
    </div> -->
    <template v-slot:left-action>
      <v-btn text color="primary" @click="Download" v-if="!disabled">
        SIMPAN + DOWNLOAD
      </v-btn>
    </template>
  </Modal>
</template>
<script>
// const JML_BANTUAN = 17300000
export default {
  data: () => ({
    xshow: false,
    datagrid: [],
    rebind: 1,
    swadaya: 0,
    grandTotal: 0,
    pilihanAtap: [
      { val: 'atap_baja', txt: 'Baja Ringan' },
      { val: 'atap_kayu', txt: 'Kayu' },
    ],
    pilihanDinding: [
      { val: 'bata_ringan', txt: 'Bata Ringan' },
      { val: 'bata_merah', txt: 'Bata Merah' },
    ],
    atap: 'atap_baja',
    dinding: 'bata_ringan',
    attrib: {
      panjang: 0,
      lebar: 0,
      atap: {
        rangka: 0,
        reng: 0,
        penutup: 0,
        nok: 0,
      },
      dinding: {
        depan: 0,
        kanan: 0,
        kiri: 0,
        belakang: 0,

        total: 0,
        plester: 0,
      },
      lantai: 0,
      gunungan: 0,
      bata: 0,
      semen: 0,
      kusen: {
        pintu: 3.864,
        jendela: 3.84,
      },
    },
  }),
  props: {
    show: Boolean,
    kodeDagri: [String, Number],
    tahun: [String, Number],
    filters: Array,
    disabled: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (val) this.Populate()
      else this.Reset()
      this.$emit('update:show', val)
    },
  },
  mounted() {
    this.Populate()
  },
  methods: {
    Reset() {
      this.atap = 'atap_baja'
      this.dinding = 'bata_ringan'
    },
    async Populate() {
      let res = await this.$api.call('BLG.SelRABDet', {
        KodeDagri: this.kodeDagri,
      })
      this.datagrid = res.data
      this.CalcGrandTotal()
    },
    SaveGrid(row) {
      row.KodeRAB = 'G.' + (this.datagrid.length - 38)
      row.Total = Math.ceil(row.Volume * row.Harga)
      row.__delete = true
      setTimeout(() => {
        this.CalcGrandTotal()
      }, 100)
    },
    CalcRow(row) {
      row.Total = Math.ceil(row.Volume * row.Harga)
      this.CalcGrandTotal()
    },
    CalcGrandTotal() {
      let total = 0,
        subTotal = { A: 0, B: 0, C: 0, D: 0, E: 0, F: 0, G: 0 },
        sub = {}
      for (let r of this.datagrid) {
        let k = r.KodeRAB.charAt(0)
        if (r.KodeRAB.length > 1) {
          subTotal[k] += r.Total
        } else {
          sub[k] = r
        }
        if (['A', 'B', 'C', 'D', 'E', 'F'].includes(k)) {
          r.__disabled = true
        }
      }
      for (let k in subTotal) {
        sub[k].Total = subTotal[k]
        total += subTotal[k]
      }
      // this.datagrid[this.datagrid.length - 2].Total = total

      let dibulatkan = Math.ceil(total / 100000) * 100000
      this.grandTotal = dibulatkan
      // this.datagrid[this.datagrid.length - 1].Total = dibulatkan
      // this.swadaya = dibulatkan > JML_BANTUAN ? dibulatkan - JML_BANTUAN : 0
      // this.rebind++
    },
    async Download() {
      // window.open(
      //   this.$api.url +
      //     '/reports/get/templates/RAB.ods?spt=BLG_SelRABDet&Tahun=' +
      //     this.tahun +
      //     '&KodeDagri=' +
      //     this.KodeDagri
      // )
      // let ret = await this.$api.post('/reports/template/RAB.ods', {
      //   sp: 'BLG_RptProfilBacklog',
      //   renderEngine: 'text',
      // })
      // if (ret.success) this.$api.download('/report/' + ret.data)
    },
    async Save() {
      let ret = await this.$api.call('BLG.SavRAB', {
        Tahun: this.tahun,
        KodeDagri: this.kodeDagri,
        JsonRAB: this.datagrid,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.ordr-1 {
  font-weight: bold;
}
</style>
