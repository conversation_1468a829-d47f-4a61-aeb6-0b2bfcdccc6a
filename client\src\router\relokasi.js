export default [
  {
    path: '/Main/Relokasi/Database',
    component: () => import('../pages/Relokasi/Database/Database.vue'),
  },
  {
    path: '/Main/Relokasi/ValidasiData',
    component: () => import('../pages/Relokasi/ValidasiData/ValidasiData.vue'),
  },
  // {
  //   path: '/Main/Relokasi/Dashboard',
  //   component: () => import('../pages/Relokasi/Dashboard/Dashboard.vue'),
  //   meta: { title: 'SIMPERUM', noauth: true },
  // },
  {
    path: '/Main/Relokasi/InputProposal',
    component: () => import('../pages/Relokasi/InputUsulan/InputUsulan.vue'),
  },
  {
    path: '/Main/Relokasi/ReviewKabupaten',
    component: () =>
      import('../pages/Relokasi/ReviewKabupaten/ReviewKabupaten.vue'),
  },
  {
    path: '/Main/Relokasi/ReviewProposal',
    component: () => import('../pages/Relokasi/ReviewUsulan/ReviewUsulan.vue'),
  },
  {
    path: '/Main/Relokasi/Pencairan',
    component: () => import('../pages/Relokasi/Pencairan/Pencairan.vue'),
  },
  {
    path: '/Main/Relokasi/LPJ',
    component: () => import('../pages/Relokasi/LPJ/LPJ.vue'),
  },
  {
    path: '/Main/Relokasi/SkGub',
    component: () => import('../pages/Relokasi/SkGub/Alokasi.vue'),
  },
  {
    path: '/Main/Relokasi/Monev',
    component: () => import('../pages/Relokasi/Monitoring/Monitoring.vue'),
  },
  {
    path: '/Main/Relokasi/Report',
    component: () => import('../pages/Relokasi/Report.vue'),
  },
]
