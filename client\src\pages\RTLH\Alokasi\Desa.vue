<template>
  <div>
    <div style="display:flex;">
      <XSelect
        dbref="PRM.SelProposal"
        :value.sync="proposal"
        :valueAsObject="true"
        width="80px"
        style="margin-right:10px;"
      />
      <XSelect
        :value.sync="forms.Kabupaten"
        style="margin-right:10px;"
        valueKey="txt"
        dbref="PRM.SelPBDTCity"
        :dbparams="{ nocache: true }"
      />
      <XSelect
        :value.sync="forms.Sumber"
        :disabled="!forms.Kabupaten"
        dbref="PRM.SelAlokasiSumber"
        width="100px"
      />
      <Checkbox
        :value.sync="hasAllocation"
        :disabled="!forms.Kabupaten"
        style="margin-left:10px; margin-top:2px;"
        text="Mempunyai Alokasi"
      />
    </div>
    <Grid
      dbref="PRM.Alokasi"
      :dbparams="params"
      :datagrid.sync="forms.XmlAlokasi"
      :filter="filterGrid"
      :disabled="true"
      :autopaging="false"
      height="calc(100vh - 240px)"
      :columns="[
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          width: '150px',
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          width: '150px',
        },
        {
          name: 'Kuota',
          value: 'Kuota',
          class: 'plain',
        },
      ]"
    >
      <template v-slot:row-Kuota="{ row }">
        <XInput type="number" :value.sync="row.Kuota" width="80px" />
      </template>
    </Grid>
    <br />
    <v-btn
      v-show="forms.Kabupaten && forms.Sumber"
      color="primary"
      @click="Save"
      >SIMPAN</v-btn
    >
  </div>
</template>
<script>
export default {
  data: () => ({
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: { XmlAlokasi: [] },
    hasAllocation: false,
  }),
  computed: {
    params() {
      let { Kabupaten, Sumber } = this.forms
      let { InputName } = this.proposal
      return { Kabupaten, Sumber, Tahun: InputName }
    },
  },
  methods: {
    async Save() {
      await this.$api.call('PRM_SavAlokasi', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
  },
}
</script>
<style lang="scss"></style>
