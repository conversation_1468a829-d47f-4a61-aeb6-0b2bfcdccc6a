export default [
  {
    path: '/Main/Backlog/Database',
    component: () => import('../pages/Backlog/Database/Database.vue'),
  },
  {
    path: '/Main/Backlog/ValidasiData',
    component: () => import('../pages/Backlog/ValidasiData/ValidasiData.vue'),
  },
  {
    path: '/Main/Backlog/Dashboard',
    component: () => import('../pages/Backlog/Dashboard/Dashboard.vue'),
    meta: { title: 'SIMPERUM', noauth: true },
  },
  {
    path: '/Main/Backlog/InputProposal',
    component: () => import('../pages/Backlog/InputUsulan/InputUsulan.vue'),
  },
  {
    path: '/Main/Backlog/ReviewKabupaten',
    component: () =>
      import('../pages/Backlog/ReviewKabupaten/ReviewKabupaten.vue'),
  },
  {
    path: '/Main/Backlog/ReviewProposal',
    component: () => import('../pages/Backlog/ReviewUsulan/ReviewUsulan.vue'),
  },
  {
    path: '/Main/Backlog/Pencairan',
    component: () => import('../pages/Backlog/Pencairan/Pencairan.vue'),
  },
  {
    path: '/Main/Backlog/LPJ',
    component: () => import('../pages/Backlog/LPJ/LPJ.vue'),
  },
  {
    path: '/Main/Backlog/SkGub',
    component: () => import('../pages/Backlog/SkGub/Alokasi.vue'),
  },
  {
    path: '/Main/Backlog/Monev',
    component: () => import('../pages/Backlog/Monitoring/Monitoring.vue'),
  },
  {
    path: '/Main/Backlog/Keuangan',
    component: () => import('../pages/Backlog/Keuangan/Keuangan.vue'),
  },
  {
    path: '/Main/Backlog/Report',
    component: () => import('../pages/Backlog/Report.vue'),
  },
]
