<template>
  <Page title="Finalisasi Usulan" :sidebar="true">
    <template v-slot:toolbar>
      <!-- <v-menu offset-y>
        <template v-slot:activator="{ on }">
          <v-icon v-tooltip="'Download Excel'" v-on="on">
            mdi-microsoft-excel
          </v-icon>
        </template>
        <v-list dense style="width: 250px">
          <v-list-item
            @click="Print(0)"
            v-if="[1, 2].includes(user.RolePositionID)"
          >
            <v-list-item-content>
              <v-list-item-title>Semua Usulan</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item
            @click="Print(1)"
            v-if="[1, 2].includes(user.RolePositionID)"
          >
            <v-list-item-content>
              <v-list-item-title>Usulan Terpilih</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="Print(2)">
            <v-list-item-content>
              <v-list-item-title> Usulan Te<PERSON>ili<PERSON> </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="PrintRekap">
            <v-list-item-content>
              <v-list-item-title> Rekap Usulan </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-divider></v-divider>
          <v-list-item @click="Print(-1)">
            <v-list-item-content>
              <v-list-item-title> Semua Data </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu> -->
    </template>
    <Sidebar :value.sync="area" />
    <div v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        :isApproved.sync="isApproved"
        @clickTambahBaru="ClickTambahBaru"
      />
      <UsulanWarning :area="area" v-show="user?.RolePositionID !== 17" />
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :requires="['Kabupaten']"
        :disabled="true"
        height="calc(100vh - 230px)"
        :doRebind="doRebind"
        :doPrint="doPrint"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            filter: {
              type: 'search',
              value: 'NIK',
            },
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'plain fix-width',
            filter: {
              type: 'search',
              value: 'Nama',
            },
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
            width: '55px',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
            filter: {
              type: 'search',
            },
          },
          {
            name: 'Kabupaten',
            value: 'Kabupaten',
            hide: true,
          },
          {
            name: 'Kecamatan',
            value: 'Kecamatan',
            hide: true,
          },
          {
            name: 'Kelurahan',
            value: 'Kelurahan',
            hide: true,
          },
          {
            name: 'Oleh:',
            value: 'FinalizedBy',
            class: 'plain',
            width: '150px',
          },
          {
            name: '',
            value: 'Dokumen',
            class: area.tabId == 2 ? '' : 'hide',
            class: 'plain',
          },
          {
            name: '',
            value: 'HasMessage',
            class: 'plain center',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox
            :value.sync="row.CheckedValue"
            checkedIcon="check_box"
            disabledIcon="mdi-lock"
            :disabled="Boolean(row.IsApproved)"
            @click="SubmitProposal(row.NIK, ...arguments)"
          />
        </template>
        <template v-slot:row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template v-slot:row-KRT_Nama="{ row }">
          <v-btn text small color="primary" @click.stop="OpenDetail(row.NoRef)">
            {{ row.KRT_Nama }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 7"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
          >
            mdi-account-check
          </v-icon>
          <v-icon v-else v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-Prioritas="{ row }">
          <v-icon
            :color="priorcolors[row.Prioritas]"
            v-tooltip="`Prioritas ${row.Prioritas}`"
          >
            mdi-numeric-{{ row.Prioritas }}-circle
          </v-icon>
        </template>
        <template v-slot:row-FinalizedBy="{ row }">
          <MenuButton
            v-if="
              (row.FinalizedBy || '').match(/perum all/i) || row.editableFinal
            "
            :menu="listDewan"
            @item-click="GantiFinalisasi($event, row)"
            :disabled="user != null && ![1, 2].includes(user.RolePositionID)"
          >
            <template v-slot="{ on }">
              <v-btn text small class="mbtn-user" v-on="on">
                {{ row.FinalizedBy }}
              </v-btn>
            </template>
          </MenuButton>
          <div v-else class="mbtn-user">{{ row.FinalizedBy }}</div>
        </template>
        <template v-slot:row-Dokumen="{ row }">
          <v-icon
            v-if="row.IsComplete"
            color="success"
            v-tooltip="'Proposal Sudah Lengkap'"
            @click.stop="OpenProposal(row.NIK)"
            >mdi-file-check</v-icon
          >
          <v-icon
            v-if="!row.IsComplete"
            @click.stop="OpenProposal(row.NIK)"
            v-tooltip="'Proposal Belum Lengkap'"
            >mdi-file-alert-outline</v-icon
          >
          <MenuButton
            :menu="finalStatuses"
            @item-click="SetFinalStatus($event, row)"
            v-if="row.CheckedValue"
            :disabled="
              user != null && ![1, 2, 3, 6, 9].includes(user.RolePositionID)
            "
          >
            <template v-slot="{ on }">
              <v-btn text small :color="GetFinalStatusColor(row)" v-on="on">
                {{ row.FinalStatus || 'Menunggu' }}
              </v-btn>
            </template>
          </MenuButton>
        </template>
        <template v-slot:row-HasMessage="{ row }">
          <v-icon
            :color="row.HasMessage == 2 ? 'green' : 'silver'"
            v-tooltip="
              row.HasMessage == 2 ? 'Ada pesan baru' : 'tidak ada pesan baru'
            "
            @click.stop="OpenMessages(row.NoRef)"
          >
            {{
              row.HasMessage ? 'mdi-message-text' : 'mdi-message-minus-outline'
            }}
          </v-icon>
        </template>
        <!-- <template v-slot:footer="{ columns }">
          <td :colspan="columns.length" v-if="!datagrid.length">
            <div style="text-align: center; padding: 12px">
              <v-btn
                text
                color="warning"
                small
                @click="$router.push('/Main/RTLH/PBDT_edit/')"
              >
                TIDAK ADA DATA YANG MEMENUHI SYARAT, SILAKAN MEMVALIDASI DATA
                TERLEBIH DAHULU
              </v-btn>
            </div>
          </td>
        </template> -->
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
        @save="doRebind++"
      />
      <ProposalDetail
        :show.sync="showProposalModal"
        :nik="selectedNIK"
        @save="doRebind++"
      />
      <Messages
        :tahun="area.Tahun"
        :noRef="selectedRef"
        :show.sync="showMessages"
      />
    </div>
  </Page>
</template>
<script>
import { mapGetters } from 'vuex'
import Sidebar from './SideBar.vue'
import DesaGlance from './DesaGlance.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import Messages from '../ReviewUsulan/Messages.vue'
import MenuButton from '@/components/Forms/MenuButton.vue'
import UsulanWarning from '../InputUsulan/UsulanWarning.vue'

export default {
  components: {
    MenuButton,
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    Messages,
    UsulanWarning,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    finalStatuses: [
      'OK',
      'Meninggal',
      'Pindah',
      'Sudah Layak Huni',
      'Tidak Mampu Swadaya',
      'Sdh Dapat APBN',
      'Sdh Dapat APBD PROV',
      'Sdh Dapat APBD KAB',
      'Sdh Dapat CSR',
      'Sdh Dapat Sumber Lainnya',
      'Sdh Dapat Dana Desa',
      'Sdh Dapat BSPS-KL',
      'Sdh Dapat BANKAB',
    ],
    dewan: [],
    listDewan: [],
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showMessages: false,
    selectedRef: null,
    doPrint: 0,
    doRebind: 0,
    selectedNIK: null,
    isApproved: null,
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    dbref() {
      return 'PRM.ProposalFinalisasi'
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue && (d.FinalStatus || 'OK') == 'OK'
      }).length
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    isApproved(val) {
      this.area = { ...this.area, isApproved: val }
    },
    '$route.query'() {
      this.area = this.$route.query
    },
  },
  mounted() {
    this.PopulateDewan()
    if (this.$route.query) this.area = this.$route.query
  },
  methods: {
    async PopulateDewan() {
      let d = await this.$api.call('Arch.SelDewan')
      this.dewan = d.data
      this.listDewan = d.data.map((w) => w.FullName)
    },
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenMessages(noRef) {
      this.selectedRef = noRef
      this.showMessages = true
    },
    GetFinalStatusColor(row) {
      return row.FinalStatus == 'OK'
        ? 'success'
        : !row.FinalStatus
        ? 'primary'
        : 'red'
    },
    async SetFinalStatus(status, row) {
      var ret = await this.$api.call('PRM.SavFinalStatus', {
        NIK: row.NIK,
        FinalStatus: status,
      })
      if (ret.success) {
        row.FinalStatus = status
      }
    },
    async GantiFinalisasi(newUser, row) {
      let d = this.dewan.find((w) => w.FullName == newUser)

      await this.$api.call('PRM.SavGantiFinalisasi', {
        ProposalDetID: row.ProposalDetID,
        NewUserId: d.UserID,
      })
      row.editableFinal = true
      row.FinalizedBy = newUser
    },
    async Print(mode) {
      let ret = await this.$api.post(`/report/Generic/xlsx`, {
        sp: 'PRM_RptProposalFinalisasi',
        groupsheet: '_groupsheet',
        Kabupaten: this.area.Kabupaten,
        Sumber: 2,
        Tahun: this.area.Tahun,
        FinalizedMode: mode,
        UserIDRef: this.user.UserID,
      })
      this.$api.download(this.$api.url + '/report' + ret.data)
    },
    async PrintRekap() {
      console.log(this.area)
      let ret = await this.$api.post(`/report/Generic/xlsx`, {
        sp: 'PRM_RptRekapFinalisasi',
        groupsheet: 'UserPemilih',
        Sumber: 2,
        Tahun: this.area.Tahun,
        FinalizedMode: [1, 2].includes(this.user.RolePositionID) ? '1' : '2',
        UserIDRef: this.user.UserID,
      })
      this.$api.download(this.$api.url + '/report' + ret.data)
    },
    async SubmitProposal(nik, checked, callback) {
      var ret = await this.$api.call('PRM.SavProposalFinalisasi', {
        Tahun: this.area.Tahun,
        ProposalID: this.area.ProposalID || parseInt(this.area.Tahun) - 2013,
        NIK: nik,
        IsAdd: checked,
        Sumber: this.area.tabId,
      })
      if (!ret.success) {
        callback(false)
      } else {
        if (this.datagrid.length == 1) this.doRebind++
      }
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
.mbtn-user {
  padding-left: 0 !important;
  width: 105px;
  overflow: hidden;
  text-overflow: ellipsis;
  justify-content: flex-start;
  .v-btn__content {
    width: 105px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.maxw-130 {
  max-width: 130px;
  text-overflow: ellipsis;
  overflow: hidden;
}
.v-list {
  max-height: 80vh;
  overflow: auto;
}
</style>
