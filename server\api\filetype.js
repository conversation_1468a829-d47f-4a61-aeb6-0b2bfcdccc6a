/*const FileType = import('file-type');
const fs = require('fs');

module.exports = {
  isValid: (filename, ext) => {
    let data = fs.readFileSync(filename)
    const type = FileType.fromBuffer(data);
      
    console.log(type)
    if (type && type.ext === 'png') {
      console.log('Valid file type');
    } else {
      console.log('Invalid file type');
    }
  }
}*/

const fs = require('fs')

module.exports = {
  isValid: (filename, ext) => {
    // console.log(filename, ext)
    if(!ext)
      ext = filename.substr(filename.lastIndexOf('.')+1)
    ext = ext.replace(/^\./,'')
    const buf = fs.readFileSync(filename)
    if(ext == 'pdf') {
      return (buf.slice(0, 5).toString() === '%PDF-' && buf.slice(-5).toString().match(/EOF/))
    }
    return true
  }
}