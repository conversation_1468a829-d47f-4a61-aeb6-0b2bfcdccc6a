const fs = require('fs');
const path = require('path');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');

// Get absolute paths to the CSV files
const dataDesaPath = path.resolve(__dirname, 'data_desa.csv');
const p1Path = path.resolve(__dirname, 'p1234.csv');

// Read the CSV files
const dataDesa = fs.readFileSync(dataDesaPath, 'utf-8');
const p1Data = fs.readFileSync(p1Path, 'utf-8');

// Parse CSV data
const dataDesaRecords = csv.parse(dataDesa, {
  columns: true,
  skip_empty_lines: true
});

const p1Records = csv.parse(p1Data, {
  columns: true,
  skip_empty_lines: true
});

// Create a map of standardized village names
const villageMap = new Map();

// Process data_desa.csv and create standardized mappings
dataDesaRecords.forEach(record => {
  const key = `${record.Kabupaten.toUpperCase()},${record.Kecamatan.toUpperCase()},${record.Desa.toUpperCase()}`;
  villageMap.set(key, record.Desa);

  // Also create alternative keys with common spelling variations
  // For example: double letters, spaces vs no spaces
  const altDesa = record.Desa.toUpperCase()
    .replace(/([A-Z])\1/g, '$1') // Remove doubled letters (e.g., SS -> S)
    .replace(/\s+/g, '');        // Remove spaces

  const altKey = `${record.Kabupaten.toUpperCase()},${record.Kecamatan.toUpperCase()},${altDesa}`;
  if (altKey !== key) {
    villageMap.set(altKey, record.Desa);
  }
});

// Create a function to find the closest match for a village name
function findClosestMatch(kabupaten, kecamatan, desa) {
  // Handle specific known cases
  if (kabupaten === 'TEGAL' && kecamatan === 'BOJONG' && desa === 'PUNCANGLUWUK') {
    const key = 'TEGAL,BOJONG,PUCANGLUWUK';
    return {value: 'PUCANGLUWUK', key, matchType: 'special_case'};
  }

  if (kabupaten === 'KLATEN' && kecamatan === 'KARANGNONGKO' && desa === 'LEGEDE') {
    const key = 'KLATEN,KARANGNONGKO,LOGEDE';
    return {value: 'LOGEDE', key, matchType: 'special_case'};
  }

  // Try exact match first
  const exactKey = `${kabupaten},${kecamatan},${desa}`;
  if (villageMap.has(exactKey)) {
    return {value: villageMap.get(exactKey), key: exactKey, matchType: 'exact'};
  }

  // Try with common variations
  const altDesa = desa.replace(/([A-Z])\1/g, '$1').replace(/\s+/g, '');
  const altKey = `${kabupaten},${kecamatan},${altDesa}`;
  if (villageMap.has(altKey)) {
    return {value: villageMap.get(altKey), key: altKey, matchType: 'variation'};
  }

  // Try just matching kabupaten and kecamatan, then find closest desa name
  for (const [key, value] of villageMap.entries()) {
    const [mapKab, mapKec, mapDesa] = key.split(',');
    if (mapKab === kabupaten && mapKec === kecamatan) {
      // Simple string similarity - if the first 4 chars match
      if (mapDesa.startsWith(desa.substring(0, 4)) || desa.startsWith(mapDesa.substring(0, 4))) {
        return {value, key, matchType: 'partial'};
      }

      // Check for common vowel substitutions (e.g., E/O, A/E)
      const desaWithVowelSubs = desa
        .replace(/E/g, 'O')  // E -> O
        .replace(/O/g, 'E')  // O -> E
        .replace(/A/g, 'E')  // A -> E
        .replace(/E/g, 'A'); // E -> A

      if (mapDesa === desaWithVowelSubs) {
        return {value, key, matchType: 'vowel_substitution'};
      }

      // Check for missing letters
      if (mapDesa.length === desa.length - 1) {
        // Try removing each letter from desa to see if it matches mapDesa
        for (let i = 0; i < desa.length; i++) {
          const modifiedDesa = desa.substring(0, i) + desa.substring(i + 1);
          if (modifiedDesa === mapDesa) {
            return {value, key, matchType: 'missing_letter'};
          }
        }
      }
    }
  }

  // Try matching same kabupaten and desa but different kecamatan
  for (const [key, value] of villageMap.entries()) {
    const [mapKab, mapKec, mapDesa] = key.split(',');
    if (mapKab === kabupaten && mapDesa === desa && mapKec !== kecamatan) {
      return {value, key, matchType: 'different_kecamatan'};
    }
  }

  // Try matching same kabupaten and similar desa but different kecamatan
  for (const [key, value] of villageMap.entries()) {
    const [mapKab, mapKec, mapDesa] = key.split(',');
    if (mapKab === kabupaten && mapKec !== kecamatan) {
      // Simple string similarity - if the first 4 chars match
      if (mapDesa.startsWith(desa.substring(0, 4)) || desa.startsWith(mapDesa.substring(0, 4))) {
        return {value, key, matchType: 'different_kecamatan_similar_desa'};
      }
    }
  }

  return null;
}

// Process and update p1.csv records
const updatedP1Records = [];
const notFoundVillages = [];
const matchTypes = {
  exact: 0,
  variation: 0,
  partial: 0,
  different_kecamatan: 0,
  different_kecamatan_similar_desa: 0,
  special_case: 0,
  vowel_substitution: 0,
  missing_letter: 0
};

// Clear the notFoundVillages array before processing
notFoundVillages.length = 0;

// Check if SISA column exists in the records
const hasSisaColumn = p1Records.length > 0 && 'SISA' in p1Records[0];
console.log(`SISA column ${hasSisaColumn ? 'exists' : 'does not exist'} in the input file`);

p1Records.forEach(record => {
  // Extract the district, subdistrict, and village names
  const kabupaten = (record['KABUPATEN/KOTA'] || '').replace(/KAB\./g, '').trim().toUpperCase();
  const kecamatan = (record['KECAMATAN'] || '').trim().toUpperCase();
  const desa = (record['DESA'] || '').trim().toUpperCase();

  const match = findClosestMatch(kabupaten, kecamatan, desa);

  if (match) {
    // Increment the counter for this match type
    matchTypes[match.matchType]++;

    // Create a new record with the standardized village name
    const updatedRecord = {...record};
    const standardizedDesa = match.value;

    if (standardizedDesa !== record['DESA'] ||
        (match.matchType.includes('different_kecamatan') && record['KECAMATAN'] !== match.key.split(',')[1])) {
      // Log the standardization with match type and original key
      console.log(`Standardized [${match.matchType}]: ${kabupaten},${kecamatan},${desa} -> ${match.key} (${standardizedDesa})`);

      // For different kecamatan matches, also update the kecamatan
      if (match.matchType.includes('different_kecamatan')) {
        const [, mapKec] = match.key.split(',');
        updatedRecord['KECAMATAN'] = mapKec;
        console.log(`  Updated kecamatan: ${kecamatan} -> ${mapKec}`);
      }

      updatedRecord['DESA'] = standardizedDesa;
    }

    // Add SISA column if it doesn't exist
    if (!hasSisaColumn) {
      updatedRecord['SISA'] = '0'; // Default value
    }

    updatedP1Records.push(updatedRecord);
  } else {
    // Add SISA column if it doesn't exist
    if (!hasSisaColumn) {
      record['SISA'] = '0'; // Default value
    }

    notFoundVillages.push(`${kabupaten},${kecamatan},${desa}`);
    updatedP1Records.push(record);
  }
});

// Write the updated data back to p1.csv
const output = stringify(updatedP1Records, {header: true});
fs.writeFileSync(p1Path, output);

// Clear the not_found_villages.log file if there are no villages that couldn't be matched
if (notFoundVillages.length > 0) {
  const notFoundLog = notFoundVillages.join('\n');
  fs.writeFileSync('not_found_villages.log', notFoundLog);
  console.log(`${notFoundVillages.length} villages not found. See not_found_villages.log for details.`);
} else {
  // Write an empty file to clear any previous content
  fs.writeFileSync('not_found_villages.log', '');
  console.log('All villages were successfully matched!');
}

console.log('\nVillage names have been standardized successfully.');
console.log(`Processed ${dataDesaRecords.length} reference village records`);
console.log(`Processed ${p1Records.length} p1 records`);
console.log(`Village map contains ${villageMap.size} entries`);

// Count how many records were actually standardized
const standardizedCount = updatedP1Records.filter(
  (record, index) => record['DESA'] !== p1Records[index]['DESA'] || record['KECAMATAN'] !== p1Records[index]['KECAMATAN']
).length;

console.log(`\n${standardizedCount} village records were standardized`);

// Report on SISA column
if (!hasSisaColumn) {
  console.log('\nSISA column was added to all records with default value of 0');
}

console.log('\nMatch type statistics:');
for (const [type, count] of Object.entries(matchTypes)) {
  console.log(`  ${type}: ${count}`);
}

// Create a summary file with all the changes made
const changes = [];
p1Records.forEach((oldRecord, index) => {
  const newRecord = updatedP1Records[index];
  if (oldRecord['DESA'] !== newRecord['DESA'] || oldRecord['KECAMATAN'] !== newRecord['KECAMATAN']) {
    changes.push(`${oldRecord['KABUPATEN/KOTA']},${oldRecord['KECAMATAN']},${oldRecord['DESA']} -> ${newRecord['KABUPATEN/KOTA']},${newRecord['KECAMATAN']},${newRecord['DESA']}`);
  }
});

if (changes.length > 0) {
  fs.writeFileSync('standardization_changes.log', changes.join('\n'));
  console.log('\nDetailed changes written to standardization_changes.log')
}