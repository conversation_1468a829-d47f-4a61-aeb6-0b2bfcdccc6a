stored procedure standard

please mind the prefix, the prefix is 3 letter before the underscore
if the table use prefix with tbl, then the stored procedure should also has TBL prefix

TABLE:
-- tbl_galeri definition

CREATE TABLE `tbl_galeri` (
  `idFoto` int(9) NOT NULL AUTO_INCREMENT,
  `judul` text NOT NULL,
  `keterangan` text NOT NULL,
  `linkFile` text NOT NULL,
  `tglUpload` date NOT NULL,
  `idUser` int(11) NOT NULL,
  PRIMARY KEY (`idFoto`),
  KEY `user_idx` (`idUser`)
);

create stored procedure for selecting by id:

CREATE PROCEDURE TBL_SelGaleri(
	_idFoto     INT
)
BEGIN
	SELECT * FROM tbl_galeri tg 
	WHERE idFoto = _idFoto
	;
END

create stored procedure for listing with search:

CREATE PROCEDURE TBL_SelGaleriList(
	_Keyword     VARCHAR(100)
)
BEGIN
	SELECT * FROM tbl_galeri tg 
	WHERE (judul LIKE CONCAT('%',_Keyword,'%') OR _Keyword IS NULL)
	;
END

create stored procedure for saving records:

CREATE PROCEDURE siwasit.TBL_SavGaleri(
	_idFoto 	INT,
	_keterangan	TEXT,
	_linkFile	TEXT,
	_tglUpload	DATE,
	
	_UserIDRef	INT
)
BEGIN
	
	IF COALESCE(idFoto, 0) = 0 THEN
	
		INSERT INTO tbl_galeri (judul, keterangan, linkFile, tglUpload, idUser)
		VALUES (_judul, _keterangan, _linkFile, _tglUpload, _UserIDRef);

	ELSE
	
		UPDATE tbl_galeri SET 
		    judul = _judul,
		    keterangan = _keterangan,
		    linkFile = _linkFile,
		    tglUpload = _tglUpload,
		    idUser = _UserIDRef
		WHERE idFoto = _idFoto;
	
	END IF;
	
	SELECT 'Data berhasil disimpan' Message;
	
END

only when the user ask to use xml for saving, then use this structure: 

CREATE PROCEDURE siwasit.TBL_SavGaleri(
	_XmlGaleri	TEXT,
	
	_UserIDRef	INT
)
BEGIN
	
	INSERT INTO tbl_galeri (judul, keterangan, linkFile, tglUpload, idUser)
	SELECT 
	  judul, 
	  keterangan, 
	  tglUpload, 
	  idUser
	FROM (
	  SELECT 
	    REPLACE(SUBSTRING_INDEX(SUBSTRING_INDEX(ExtractValue(_XmlCashFlow, '//row/@judul'), ' ', Number), ' ', -1),'~',' ') AS judul,
	    REPLACE(SUBSTRING_INDEX(SUBSTRING_INDEX(ExtractValue(_XmlCashFlow, '//row/@keterangan'), ' ', Number), ' ', -1),'~',' ') AS keterangan,
	    REPLACE(SUBSTRING_INDEX(SUBSTRING_INDEX(ExtractValue(_XmlCashFlow, '//row/@tglUpload'), ' ', Number), ' ', -1),'~',' ') AS tglUpload,
	    _UserIDRef AS idUser
	  FROM
	    arch_number
	  WHERE
	    Number BETWEEN 1 AND ExtractValue(_XmlCashFlow, 'count(//row)')
	) sub
	ON DUPLICATE KEY UPDATE
	  judul = VALUES(judul),
	  keterangan = VALUES(keterangan),
	  tglUpload = VALUES(tglUpload),
	  idUser = VALUES(idUser)
	;
	
	SELECT 'Data berhasil tersimpan' Message;
	
END


create stored procedure for deleting records:

CREATE PROCEDURE siwasit.TBL_DelGaleri(
	_idFoto 	INT
)
BEGIN
	
	DELETE FROM tbl_galeri 
	WHERE idFoto = _idFoto
	;
	
	SELECT 'Data berhasil dihapus' Message;
	
END