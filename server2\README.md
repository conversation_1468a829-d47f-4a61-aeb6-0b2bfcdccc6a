# Fastify Server Project

This project has been converted from Express to Fastify framework while maintaining the same API functionality.

## Features

- **Fastify Framework**: High-performance web framework for Node.js
- **Database Integration**: MySQL database connection with stored procedure support
- **Authentication Middleware**: Configurable auth middleware
- **Event System**: Notification handling system
- **Error Handling**: Comprehensive error handling and logging

## Project Structure

```
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
├── .env.example          # Environment variables template
├── api/
│   ├── call.js           # API routes (converted from Express to Fastify)
│   ├── auth.js           # Authentication middleware
│   └── events.js         # Event handling system
└── common/
    ├── db.js             # Database connection and utilities
    ├── dbconfig.js       # Database configuration
    └── ...               # Other common utilities
```

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual database credentials
   ```

3. **Start the server:**
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### POST `/api/call/:sp`
Execute stored procedures with authentication.

**Parameters:**
- `sp`: Stored procedure name

**Request Body:**
- JSON object with parameters for the stored procedure

**Response:**
```json
{
  "success": true|false,
  "data": "result data",
  "type": "array|error",
  "message": "status message"
}
```

### GET `/health`
Health check endpoint.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

## Key Changes from Express to Fastify

1. **Router → Plugin**: Express router converted to Fastify plugin system
2. **Middleware**: Express middleware converted to Fastify hooks and decorators
3. **Request/Response**: Updated to use Fastify's request/reply objects
4. **Error Handling**: Adapted to Fastify's error handling patterns
5. **App Locals**: Replaced `req.app.locals` with Fastify decorators

## Development

The server runs on `http://localhost:3000` by default. You can change the port by setting the `PORT` environment variable.

## Dependencies

- **fastify**: Web framework
- **@fastify/cors**: CORS support
- **mysql**: Database driver
- **moment**: Date/time utilities