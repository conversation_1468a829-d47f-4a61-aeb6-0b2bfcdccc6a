<template>
  <div class="sidebar-validasi">
    <div style="display: flex; font-size: 12px" v-if="tabs.length">
      <div
        class="tab"
        v-show="tabs.includes(2)"
        @click="tabId = 2"
        :class="{
          active: tabId == 2,
        }"
      >
        PB
      </div>
      <div
        class="tab"
        v-show="tabs.includes(1)"
        @click="tabId = 1"
        :class="{
          active: tabId == 1,
        }"
      >
        BP2BT
      </div>
      <div
        class="tab"
        v-show="tabs.includes(4)"
        @click="tabId = 4"
        :class="{
          active: tabId == 4,
        }"
      >
        CSR
      </div>
    </div>
    <div style="padding: 10px; display: flex">
      <XSelect
        v-show="!searchMode"
        dbref="BLG.SelProposal"
        :value.sync="proposal"
        :valueAsObject="true"
        :selectFirstOption="true"
        width="80px"
        style="margin-right: 10px"
      />
      <XSelect
        v-show="!searchMode"
        dbref="BLG.SelBacklogArea"
        :dbparams="{ nocache: true, Tahun: proposal.InputName }"
        :value.sync="kabupaten"
        :valueAsObject="true"
        width="190px"
      />
      <XInput
        type="text"
        v-show="searchMode"
        :value.sync="keyword"
        placeholder="Cari .."
        width="280px"
      />
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="!searchMode"
        @click="searchMode = !searchMode"
        >search</v-icon
      >
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="searchMode"
        @click="searchMode = !searchMode"
        >clear</v-icon
      >
    </div>
    <div>
      <List
        :dbref="dbref"
        :dbparams="listParams"
        :requires="['Kabupaten']"
        :rebind="rebind"
        :filters="{
          keyword: keyword,
          filter: filterArea,
        }"
        :height="`calc(100vh - ${!tabs.length ? 160 : 200}px)`"
        @itemClick="AreaClicked"
        :selectOnLoad="true"
      >
        <template v-slot="{ row }">
          <div :class="`ordr-${row.Ordr}`">
            <div>
              <v-icon
                v-show="row.Ordr == 2"
                style="height: 16px"
                :style="{ color: warnaDesa[row.Priority] || 'palegreen' }"
              >
                mdi-circle-medium
              </v-icon>
              {{ row.KelTipe == 'KEL' ? 'K.' : '' }} {{ row.AreaName }}
            </div>
            <div class="status">
              <div
                :class="`badge s-${
                  row.Rekom ? row.Rekom.replace(' ', '') : ''
                }`"
              >
                {{ row.Rekom }}
              </div>
              <slot name="badge" :row="row">
                <div class="badge s-jml" v-tooltip="'Jml Disetujui/Jml Usulan'">
                  {{ row.JmlBansos }} / {{ row.JmlPB }}
                </div>
              </slot>
            </div>
          </div>
        </template>
      </List>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data: () => ({
    kabupaten: { val: null, txt: null },
    proposal: {},
    selectedArea: {},
    searchMode: false,
    keyword: '',
    tabId: 2,
    warnaDesa: ['palegreen', 'red', 'yellow', 'palegreen'],
  }),
  props: {
    value: Object,
    dbref: {
      type: String,
      default: 'BLG.SelProposalArea',
    },
    tabs: {
      type: Array,
      default: () => [2, 1, 9, 4],
    },
    rebind: Number,
    filter: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    value(val) {
      if (val.Tahun) this.proposal = { InputName: val.Tahun }
      if (val.Kabupaten) this.kabupaten = { txt: val.Kabupaten }
    },
    searchMode(val) {
      if (!val) this.keyword = ''
    },
  },
  computed: {
    listParams() {
      return {
        Kabupaten: this.kabupaten.txt,
        SumberID: this.tabId,
        ProposalID: this.proposal.ProposalID,
        ...this.filter,
      }
    },
  },
  mounted() {
    if (Object.keys(this.$route.query).length) {
      this.area = this.$route.query
    } else if (window.sessionStorage.getItem('side-area')) {
      setTimeout(() => {
        let areaVal = JSON.parse(window.sessionStorage.getItem('side-area'))
        this.$emit('update:value', areaVal)
      }, 500)
    }
  },
  methods: {
    ...mapActions(['setPageFocused']),
    AreaClicked(item) {
      if (!item || !item.AreaID) return
      this.setPageFocused(true)
      const areaVal = {
        tabId: this.tabId,
        Sumber: this.tabId,
        Tahun: this.proposal.InputName,
        ProposalID: this.proposal.ProposalID,
        Kabupaten: this.kabupaten.txt,
        Kecamatan: item.Kecamatan,
        Kelurahan: item.AreaName,
        KelurahanID: item.AreaID,
        KodeDagri: item.KodeDagri,
      }
      window.sessionStorage.setItem('side-area', JSON.stringify(areaVal))
      this.$emit('update:value', areaVal)
    },
    filterNotEmpty(item) {
      return item.Approved || !this.filter.notEmpty
    },
    filterArea(item) {
      return item.AreaName.match(new RegExp(this.keyword, 'i'))
      // &&
      // this.filterNotEmpty(item)
    },
  },
}
</script>
<style lang="scss">
.sidebar-validasi {
  background: white;
  .tab {
    text-align: center;
    flex: 1;
    font-size: 13px;
    padding: 5px 10px;
    background: #ddd;
    color: gray;
    border-right: 0.5px solid silver;
    cursor: pointer;

    &.active {
      background: white;
      font-weight: bold;
      border: 1px solid silver;
      border-bottom: transparent;
    }
  }
}
</style>
