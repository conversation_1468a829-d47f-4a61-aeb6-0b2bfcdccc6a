var express = require("express");
const path = require("path");
const fs = require("fs");
const carbone = require("carbone");
var db = require("../../common/db");
// var uji = require("./uji");
var moment = require("moment");
var router = express.Router();
var Excel = require("exceljs");
const shell = require("shelljs");
moment.locale("id");
// CARBONE specific setup
carbone.addFormatters({
  // this formatter can be used in a template with {d.myBoolean:yesOrNo()}
  format: function(val, fmt) {
    if (val instanceof Date) {
      return moment(val).format(fmt.replace(/_/g, " "));
    } else if (!isNaN(val)) {
      return parseFloat(val)
        .toFixed(2)
        .replace(/\d(?=(\d{3})+\.)/g, "$&.")
        .replace(/\.00$/, " ");
    } else {
      return val;
    }
  },
  boolstr: function(val, fmt) {
    var noyes = fmt.split("|");
    if (val === undefined || val === null || val === "") return "";
    else if (val === 0 || val === false) return noyes[0];
    else return noyes[1];
  }
});

// // middleware that is specific to this router
// router.use(function app_mid(req, res, next) {
//   var s = req.session;
//   if (!req.body._UserIDRef && s.user && s.user.UserID)
//     req.body._UserIDRef = s.user.UserID;

//   next();
// });

const report = {
  async Generic(req, res, type) {
    try {
      res.header(
        "Cache-Control",
        "private, no-cache, no-store, must-revalidate"
      );
      res.header("Expires", "-1");
      res.header("Pragma", "no-cache");
      let param = {...req.query, ...req.body};

      let rpt_name = param.rptname ? param.rptname : "Report";
      let workbook = new Excel.Workbook();

      var sets = 1,
        i = 0;
      if (req.query.sets) sets = req.query.sets;

      do {
        let postfix = i > 0 ? "_s" + (i + 1) : "";
        let data = await db.exec(param.sp + postfix, param);

        report.GenericRun(data, workbook, {
          name: rpt_name,
          sheet: i,
          groupsheet: req.query.groupsheet
        });

        i++;
      } while (i < sets);

      let dd = moment().format("mmss");
      workbook.xlsx.writeFile(`tmp/generic${dd}.xlsx`).then(() => {
        if (type == "pdf" && this.GeneratePDF(`tmp/generic${dd}.xlsx`)) {
          res.send({
            success: true,
            data: `/report/get/generic${dd}.pdf`,
            message: "Report Generated",
            type: "url"
          });
        } else {
          res.send({
            success: true,
            data: `/report/get/generic${dd}.xlsx`,
            message: "Report Generated",
            type: "url"
          });
        }
      });
    } catch (err) {
      res.status(400).send({
        error: err.message
      });
    }
  },
  GeneratePDF(path) {
    // console.log(`Converting to PDF: ${path}..`);
    var out = shell.exec(
      `/opt/libreoffice7.0/program/soffice --convert-to pdf "/app/${path}" --outdir /app/tmp`,
      {
        silent: true
      }
    );
    if (out.code != 0) {
      console.error(`ERR: ${out.stderr}`);
      return false;
    } else return true;
  },
  GenericRun(res, workbook, opts) {
    try {
      // Common columns width
      let s_header = {
        border: {
          top: {style: "thin"},
          left: {style: "thin"},
          bottom: {style: "thin"},
          right: {style: "thin"}
        },
        font: {size: 10, bold: true},
        alignment: {vertical: "middle", horizontal: "center"}
      };
      let widths = [
        {No: 6},
        {NIK: 15},
        {Nama: 24},
        {Alamat: 50},
        {Kabupaten: 15},
        {Kecamatan: 15},
        {Kelurahan: 15},
        {Desa: 15}
      ];

      if (opts["groupsheet"]) {
        let group_col = opts["groupsheet"];
        let group_val = "";

        let hcol = [];
        let col_w = [];
        // let sb_rows = "array(";
        // i = 1;
        // foreach (res[0] as $k => $v)
        // {
        //     array_push(hcol, str_replace("_", " ", $k));
        //     col_w[''.i] = array_key_exists($k, $widths)? $widths[$k]: ceil(strlen($k)*1.5);
        //     $sb_rows .= '$row["'.$k.'"],';
        //     i++;
        // }sb_rows
        // $sb_rows .= '"")';

        let i = 0;
        res.forEach(row => {
          if (group_val != row[group_col]) {
            group_val = row[group_col];
            // workbook.setSheetOptions([
            //     'colwidth' => col_w,
            //     'styles' => [$s_title, s_header, $s_group, $s_body, $s_foot]
            // ]);
            workbook.addWorksheet(group_val);
            workbook.addRow(hcol, "i"); // header
          }

          let rcol_arr = [];
          for (let k in row) {
            // if(!isNaN(row[k]) && String(row[k]).length < 15)
            //     rcol_arr, ['value'=>floatval(row[k]), 'style'=>['NumFormat'=>1]]);
            // else
            rcol_arr.push(row[k]);
          }
          workbook.addRow($rcol_arr, "i"); // body
          // workbook.addRowWithStyle(eval($sb_rows), $s_body);
          // i++;
          // if(i == 10) break;
        });
      } else {
        if (res.length > 0) {
          let hcol = [];
          let hcol2 = [];
          let col_w = [];
          let i = 1;
          keys = Object.keys(res[0]);
          let sheet = workbook.addWorksheet(opts.name);

          sheet.pageSetup.fitToPage = true;
          sheet.pageSetup.fitToWidth = 1;
          sheet.pageSetup.fitToHeight = 0;
          sheet.pageSetup.paperSize = 9;
          sheet.pageSetup.orientation = "landscape";
          sheet.pageSetup.margins = {
            left: 0.25,
            right: 0.25,
            top: 0.5,
            bottom: 0.5,
            header: 0.3,
            footer: 0.3
          };

          for (i = 0; i < keys.length; i++) {
            let colspan = 1;
            let j = i;
            let c1 = "",
              c2 = "";
            do {
              if (j + 1 >= keys.length) break;
              c1 = keys[j].split("_", 2);
              c2 = keys[j + 1].split("_", 2);
              if (c1[0] == c2[0]) colspan++;
              j++;
            } while (c1[0] == c2[0]);

            if (colspan == 1) {
              sheet.mergeCells(1, i + 1, 2, i + 1);
              sheet.getCell(1, i + 1).value = keys[i].replace("_", " ");
              sheet.getColumn(i + 1).width =
                widths[keys[i]] || Math.ceil(keys[i].length * 1.5);
              sheet.getCell(1, i + 1).border = s_header.border;
              sheet.getCell(2, i + 1).alignment = s_header.alignment;
              // cw = array_key_exists(keys[i], $widths)? $widths[keys[i]]: ceil(strlen(keys[i])*1.5);
              // col_w[''.(i+1)] = cw < 8? 8: cw;
            } else {
              // // array_push(hcol, ['value'=>c1[0], 'colspan'=>colspan]);
              sheet.mergeCells(1, i + 1, 1, i + colspan);
              sheet.getCell(1, i + 1).value = c1[0].replace("_", " ");
              sheet.getCell(1, i + 1).border = s_header.border;
              sheet.getCell(1, i + 1).alignment = s_header.alignment;
              let len = colspan + i;
              while (i < len) {
                let c2 = keys[i].split("_", 2);
                sheet.getCell(2, i + 1).value = c2[1].replace("_", " ");
                sheet.getCell(2, i + 1).border = s_header.border;
                sheet.getCell(2, i + 1).alignment = s_header.alignment;

                // sheet.getColumn(i).width = 20;
                // hcol2.push(c2[1].replace("_", " "));
                // cw = array_key_exists(c2[1], $widths)? $widths[c2[1]]: ceil(strlen(c2[1])*1.5);
                // col_w[''.(i+1)] = cw < 8? 8: cw;
                i++;
              }
              i--;
            }
          }

          sheet.getRow(1).font = s_header.font;
          sheet.getRow(2).font = s_header.font;

          // var_dump(hcol);
          // var_dump(hcol2);
          // ob_start();
          // var_dump(hcol2);
          // $s = ob_get_clean();
          // $stdout = fopen ("php://stdout","w");
          // fwrite($stdout, hcol2 . PHP_EOL);
          // foreach (res[0] as $k => $v)
          // {
          //     array_push(hcol, str_replace("_", " ", $k));
          //     col_w[''.i] = array_key_exists($k, $widths)? $widths[$k]: ceil(strlen($k)*1.5);
          //     i++;
          // }

          // workbook.setSheetOptions([
          //     'colwidth' => col_w,
          //     'styles' => [$s_title, s_header, $s_group, $s_body, $s_foot]
          // ]);

          // sheet.addRow(hcol); // header
          // sheet.addRow(hcol2); // header
          res.forEach(row => {
            let rcol_arr = [];
            for (let key in row) {
              // if(is_numeric(row[key]) && strlen(row[key]) < 15)
              //     array_push($rcol_arr, ['value'=>floatval($value), 'style'=>['NumFormat'=>1]]);
              // else
              rcol_arr.push(row[key]);
            }
            sheet.addRow(rcol_arr); // body
          });
        }
      }
    } catch (ex) {
      console.error(ex);
    }
  }
};

router.post("/GetParamsVue", async function(req, res) {
  var dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.body.sp}' AND PARAMETER_MODE = 'IN'`
  );

  let sb = [];
  for (idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME; //.substr(1);
    if (param_name.substr(-3) !== "Ref") {
      var o = {};
      o["id"] = param_name;
      if (param_name.substr(-2) == "ID")
        //sb += '"Name":"' + param_name.substr(8, param_name.length - 10) + '",';
        o["text"] = param_name.substr(8, param_name.length - 10);
      // sb += '"Name":"' + param_name.substr(1) + '",';
      else o["text"] = param_name.substr(1);
      if (param_name.substr(-2) === "ID") {
        // sb +=
        //   '"url":"api/call/' +
        //   param_name.substr(1, param_name.length - 3) +
        //   '",';
        o["dbref"] = param_name.substr(1, param_name.length - 3);
        //db += '"Type":"Select",';
        o["type"] = "Select";
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "varchar"
      ) {
        // db += '"Type":"Input",';
        o["type"] = "Input";
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "date"
      ) {
        // db += '"Type":"Date",';
        o["type"] = "Date";
      } else if (
        dbp[idx].DATA_TYPE == "boolean" ||
        dbp[idx].DATA_TYPE == "tinyint"
      ) {
        // db += '"Type":"Checkbox",';
        o["type"] = "Checkbox";
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"';
      }
      // db += "},";
      sb.push(o);
    }
  }
  // sb += "{";
  // sb += '"Id":"btnReport",';
  // sb += '"Name":"",';
  // sb += '"Type":"VBtn"';
  // sb += "}";
  // sb += "]";

  res.send({
    success: true,
    data: sb,
    message: "",
    type: "array"
  });
});

router.post("/GetParams", async function(req, res) {
  var dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.query.sp}' AND PARAMETER_MODE = 'IN'`
  );

  let sb = "[";
  for (idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME; //.substr(1);
    if (param_name.substr(-3) !== "Ref") {
      sb += "{";
      sb += '"Id":"' + param_name + '",';
      if (param_name.substr(-2) == "ID")
        sb += '"Name":"' + param_name.substr(8, param_name.length - 10) + '",';
      else sb += '"Name":"' + param_name.substr(1) + '",';
      if (param_name.substr(-2) === "ID") {
        sb +=
          '"url":"api/call/' +
          param_name.substr(1, param_name.length - 3) +
          '",';
        db += '"Type":"select",';
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "varchar"
      ) {
        db += '"Type":"text",';
      } else if (
        dbp[idx].DATA_TYPE == "datetime" ||
        dbp[idx].DATA_TYPE == "date"
      ) {
        db += '"Type":"date",';
      } else if (
        dbp[idx].DATA_TYPE == "boolean" ||
        dbp[idx].DATA_TYPE == "tinyint"
      ) {
        db += '"Type":"checkbox",';
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"';
      }
      db += "},";
    }
  }
  sb += "{";
  sb += '"Id":"btnReport",';
  sb += '"Name":"",';
  sb += '"Type":"button"';
  sb += "}";
  sb += "]";

  res.send({
    Success: true,
    Data: sb,
    Message: "",
    Type: "array"
  });
});

router.post("/Generic/:type", async function(req, res) {
  // let rid = req.query.rid.split("/");
  await report.Generic(req, res, req.params.type);
});

router.get("/get/:file", async function(req, res) {
  res.sendFile(path.resolve("tmp/" + req.params.file));
});

router.get("/get/templates/:file", async function(req, res) {
  res.sendFile(path.resolve("tmp/templates/" + req.params.file));
});

router.post("/template/:file", async function(req, res) {
  let data = await db.exec("PRM_RptKuisoner", req.body);
  // console.log(data);
  carbone.render("tmp/templates/" + req.params.file, data, function(
    err,
    result
  ) {
    if (err) {
      return console.log(err);
    }
    // write the result
    fs.writeFileSync("tmp/" + req.params.file, result);
    res.send({
      success: true,
      data: `/get/${req.params.file}`,
      message: "Report Generated",
      type: "url"
    });
  });
});

router.post("/generate/:type", async function(req, res) {
  try {
    if (req.body.headers) {
      let data = req.body.data;
      if (req.body.sp) {
        data = await db.exec(req.body.sp, req.body);
        data = data.map(d => {
          let r = [];
          for (let i = 0; i < req.body.cols.length; i++) {
            r.push(d[req.body.cols[i]]);
          }
          return r;
        });
      }

      // console.log("Generating tables..");
      let workbook = new Excel.Workbook();
      let ws = workbook.addWorksheet(req.body.name || "Report");

      ws.pageSetup.fitToPage = true;
      ws.pageSetup.fitToWidth = 1;
      ws.pageSetup.fitToHeight = 0;
      ws.pageSetup.paperSize = 9;
      ws.pageSetup.orientation = "landscape";
      ws.pageSetup.margins = {
        left: 0.25,
        right: 0.25,
        top: 0.5,
        bottom: 0.5,
        header: 0.3,
        footer: 0.3
      };

      ws.addTable({
        name: req.body.name || "Report",
        ref: "A1",
        headerRow: true,
        totalsRow: true,
        style: {
          theme: "TableStyleLight8",
          showRowStripes: true
        },
        columns: req.body.headers,
        rows: data
      });
      let dd = moment().format("mmss");
      workbook.xlsx.writeFile(`tmp/tables${dd}.xlsx`).then(() => {
        if (
          req.params.type == "pdf" &&
          report.GeneratePDF(`tmp/tables${dd}.xlsx`)
        ) {
          res.send({
            success: true,
            data: `/get/tables${dd}.pdf`,
            message: "Report Generated",
            type: "url"
          });
        } else {
          res.send({
            success: true,
            data: `/get/tables${dd}.xlsx`,
            message: "Report Generated",
            type: "url"
          });
        }
      });
    } else if (req.query.sp || req.body.sp) {
      await report.Generic(req, res, req.params.type);
    } else {
      res.send({
        success: true,
        data: null,
        message: "Cant Generate Report",
        type: "error"
      });
    }
  } catch (ex) {
    res.send({
      success: false,
      data: ex.message,
      message: "Error while generating report",
      type: "error"
    });
  }
});

// router.get("/permohonan/:id", uji.rptPermohonan);
// router.get("/permohonan-paid/:id", uji.rptPermohonanPaid);
// router.get("/permohonanonline/:id", uji.rptPermohonanOnline);
// router.get("/spu/:id", uji.rptPerintahUji);

module.exports = router;
