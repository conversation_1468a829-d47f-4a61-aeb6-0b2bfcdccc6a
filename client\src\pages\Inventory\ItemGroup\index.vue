<template>
  <div style="padding:10px;">
    <Grid
      :datagrid="datatable"
      style="width:500px; height:calc(100vh - 100px);"
      @dataUpdate="handleDataUpdate"
      @dataDelete="handleDataDelete"
    >
      <template v-slot:header>
        <v-row>
          <v-col>
            Kode
          </v-col>
          <v-col cols="4">
            Nama Group
          </v-col>
          <v-col cols="4">
            Parent
          </v-col>
          <v-col cols="2" center>
            * Finance
          </v-col>
        </v-row>
      </template>
      <template v-slot:content="{ item }">
        <v-col>
          {{ item.GroupCode }}
        </v-col>
        <v-col cols="4">
          {{ item.GroupName }}
        </v-col>
        <v-col cols="4">
          {{ item.ParentName }}
        </v-col>
        <v-col cols="2" center>
          <v-icon>{{
            item.IncFin ? "check_box" : "check_box_outline_blank"
          }}</v-icon>
        </v-col>
      </template>
      <template v-slot:insert="{ item }">
        <v-col>
          <XInput :value.sync="item.GroupCode" />
        </v-col>
        <v-col cols="4">
          <XInput :value.sync="item.GroupName" />
        </v-col>
        <v-col cols="4">
          <XSelect
            :items="parentItems"
            :value.sync="item.ParentID"
            :text.sync="item.ParentName"
          ><XSelect>
        </v-col>
        <v-col cols="2" center>
          <v-checkbox v-model="item.IncFin" class="no-label"></v-checkbox>
        </v-col>
      </template>
    </Grid>
  </div>
</template>

<script>
//import Vue from "vue";
import { Input, Select } from "@/components/Forms";
import Grid from "@/components/Grid";
import "material-design-icons-iconfont/dist/material-design-icons.css";

export default {
  components: {
    Input,
    Select,
    Grid,
  },
  data: () => ({
    files: [],
    datatable: null,
    parentItems: [],
  }),
  created() {
    this.getItemGroup();
    this.getParentItems();
  },
  methods: {
    async getItemGroup() {
      let ret = await this.$api.select("itemgroup");
      this.datatable = ret.data;
    },
    async getParentItems() {
      this.parentItems = (await this.$api.select("itemgroup")).toDropdown();
    },
    async handleDataUpdate(idx, data) {
      let ret = await this.$api.call("INV_SavItemGroup", data);
      if (!ret.success) this.getItemGroup();
    },
    async handleDataDelete(idx) {
      let ret = await this.$api.call("INV_DelItemGroup", this.datatable[idx]);
      if (ret.success) this.datatable.splice(idx, 1);
    },
  },
};
</script>
