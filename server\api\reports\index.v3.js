var express = require('express')
var router = express.Router()
var Reporter = require('./generator')
const path = require('path')
const fs = require('fs')
const carbone = require('carbone')
var db = require('../../common/db')
var moment = require('moment')
var Excel = require('exceljs')
const shell = require('shelljs')
const custom = require('./custom')
const HtmlGenerator = require('./HtmlGenerator')
moment.locale('id')

// CARBONE specific setup
carbone.addFormatters({
  // this formatter can be used in a template with {d.myBoolean:yesOrNo()}
  format: function(val, fmt) {
    if (val instanceof Date) {
      return moment(val).format(fmt.replace(/_/g, ' '))
    } else if (!isNaN(val)) {
      return parseFloat(val)
        .toFixed(2)
        .replace(/\d(?=(\d{3})+\.)/g, '$&.')
        .replace(/\.00$/, ' ')
    } else {
      return val
    }
  },
  boolstr: function(val, fmt) {
    var noyes = fmt.split('|')
    if (val === undefined || val === null || val === '') return ''
    else if (val === 0 || val === false) return noyes[0]
    else return noyes[1]
  },
})

const report = {
  colswidth: {
    No: 6,
    NIK: 17,
    Nama: 24,
    KRT_Nama: 24,
    Alamat: 50,
    Kabupaten: 15,
    Kecamatan: 15,
    Kelurahan: 15,
    Desa: 15,
  },
  async Generic(req, res, type) {
    try {
      res.header(
        'Cache-Control',
        'private, no-cache, no-store, must-revalidate'
      )
      res.header('Expires', '-1')
      res.header('Pragma', 'no-cache')
      let param = {...req.query, ...req.body}

      let rpt_name = param.rptname ? param.rptname : 'Report'
      let dd = moment().format('mmss')
      let filename = rpt_name.replace(/[^a-z0-9]/gi, '_') + dd
      const options = {
        filename: `tmp/${filename}.${type || 'xlsx'}`,
        useStyles: true,
        useSharedStrings: true,
      }
      let workbook = {}
      if (type === 'html') workbook = new HtmlGenerator(options)
      else workbook = new Excel.stream.xlsx.WorkbookWriter(options)

      var sets = 1,
        i = 0
      if (param.sets) sets = param.sets

      do {
        let postfix = i > 0 ? '_s' + (i + 1) : ''
        // console.log(param.sp + postfix)
        // let data = await db.exec(param.sp + postfix, param);
        // console.log(`Finished loading ${data.count} rows.`)
        await report.GenericRun(param.sp + postfix, param, workbook, {
          name: rpt_name,
          sheet: i,
          headers: param.headers,
          groupsheet: param.groupsheet,
        })

        i++
      } while (i < sets)

      await workbook.commit()
      if (type == 'pdf' && this.GeneratePDF(`tmp/${filename}.xlsx`)) {
        res.send({
          success: true,
          data: `/get/${filename}.pdf`,
          message: 'Report Generated',
          type: 'url',
        })
      } else {
        res.send({
          success: true,
          data: `/get/${filename}.${type || 'xlsx'}`,
          message: 'Report Generated',
          type: 'url',
        })
      }
    } catch (err) {
      res.send({
        success: false,
        message: err.message,
      })
    }
  },
  GeneratePDF(path) {
    console.info(`Converting to PDF: ${path}..`)
    var out = shell.exec(
      `/opt/libreoffice7.0/program/soffice --convert-to pdf "/app/${path}" --outdir /app/tmp`,
      {
        silent: true,
      }
    )
    if (out.code != 0) {
      console.error(`ERR: ${out.stderr}`)
      return false
    } else return true
  },
  GenerateHeader(sheet, keys) {
    let s_header = {
      border: {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'},
      },
      font: {size: 10, bold: true},
      alignment: {vertical: 'middle', horizontal: 'center'},
    }
    // Generating Dynamic Headers
    for (let i = 0; i < keys.length; i++) {
      if(keys[i].match(/^_/)) continue
      let colspan = 1
      let j = i
      let c1 = '',
        c2 = ''
      do {
        if (j + 1 >= keys.length) break
        c1 = keys[j].split('_', 2)
        c2 = keys[j + 1].split('_', 2)
        if (c1[0] == c2[0]) colspan++
        j++
      } while (c1[0] == c2[0])

      if (colspan == 1) {
        sheet.mergeCells(1, i + 1, 2, i + 1)
        sheet.getCell(1, i + 1).value = keys[i].replace(/_/g, ' ')
        // console.log(keys[i].replace(/_/g, ' '))
        sheet.getColumn(i + 1).width =
          this.colswidth[keys[i]] || Math.ceil(keys[i].length * 1.5)
        sheet.getCell(1, i + 1).border = s_header.border
        sheet.getCell(2, i + 1).alignment = s_header.alignment
      } else {
        // // array_push(hcol, ['value'=>c1[0], 'colspan'=>colspan]);
        sheet.mergeCells(1, i + 1, 1, i + colspan)
        sheet.getCell(1, i + 1).value = c1[0].replace(/_/g, ' ')
        // console.log(c1[0].replace(/_/g, ' '))
        sheet.getCell(1, i + 1).border = s_header.border
        sheet.getCell(1, i + 1).alignment = s_header.alignment
        let len = colspan + i
        while (i < len) {
          let c2 = keys[i].split('_', 2)
          sheet.getCell(2, i + 1).value = c2[1].replace(/_/g, ' ')
          // console.log(c2[1].replace(/_/g, ' '))
          sheet.getCell(2, i + 1).border = s_header.border
          sheet.getCell(2, i + 1).alignment = s_header.alignment
          i++
        }
        i--
      }
    }
    sheet.getRow(2).font = s_header.font
    sheet.getRow(1).font = s_header.font
  },
  async GenericRun(sp, param, workbook, opts) {
    let s_header = {
      border: {
        top: {style: 'thin'},
        left: {style: 'thin'},
        bottom: {style: 'thin'},
        right: {style: 'thin'},
      },
      font: {size: 10, bold: true},
      alignment: {vertical: 'middle', horizontal: 'center'},
    }
    let hasPagination = await db.spHasPagination(sp)
    console.log('GenericRun')
    try {
      let i = 0,
        ii = 0
      let pageRef = 0
      // Common columns width
      if (opts.groupsheet) {
        console.log(': with groupsheet')
        let group_col = opts.groupsheet
        let group_val = ''

        let sheet = null
        let res = await db.exec(sp, param)
        let row = res[ii]

        do {
          if (group_val != row[group_col]) {
            group_val = row[group_col]
            sheet = workbook.addWorksheet(group_val)
            this.GenerateHeader(sheet, Object.keys(row))
          }

          let rcol_arr = []
          for (let k in row) {
            if(k.match(/^_/)) rcol_arr.push('')
            else rcol_arr.push(row[k])
          }
          sheet.addRow(rcol_arr) // body
          if (ii < res.length) {
            ii++
            row = res[ii]
          } else if (hasPagination) {
            ii = 0
            pageRef++
            console.log(`page ${pageRef}: ${sp}`)
            res = await db.exec(sp, {
              ...param,
              _PageRef: pageRef,
              _CountRef: 10000,
            })
            if (res.length === 10000 && hasPagination) row = res[ii]
            else row = null
          } else {
            row = null
          }
        } while (row)
      } else {
        console.log(': no groupsheet')
        let res = await db
          .exec(sp, {...param, _PageRef: pageRef, _CountRef: 50000}, true)
          .catch(err => console.error(err))

        if (res.length > 0) {
          console.log(`generating ${res.length} rows`)
          let keys = Object.keys(res[0])
          let sheet = workbook.addWorksheet('Report')

          sheet.pageSetup.fitToPage = true
          sheet.pageSetup.fitToWidth = 1
          sheet.pageSetup.fitToHeight = 0
          sheet.pageSetup.paperSize = 9
          sheet.pageSetup.orientation = 'landscape'
          sheet.pageSetup.margins = {
            left: 0.25,
            right: 0.25,
            top: 0.5,
            bottom: 0.5,
            header: 0.3,
            footer: 0.3,
          }
          let i = 0,
            ii = 0
          let pageRef = 0
          let row = res[ii]

          if (opts.headers) {
            sheet.addRow(
              opts.headers.map((h, idx) => {
                sheet.getColumn(idx + 1).width = this.colswidth[h.value]
                  ? this.colswidth[h.value]
                  : String(row[h.value]).length * 1.3 < 6
                    ? 6
                    : String(row[h.value]).length * 1.3
                return h.name
              })
            )
            sheet.getRow(1).font = s_header.font
            sheet.getRow(1).commit()

            do {
              let rcol_arr = []
              for (let i in opts.headers) {
                rcol_arr.push(row[opts.headers[i].value])
              }
              sheet.addRow(rcol_arr).commit() // body
              if (ii < res.length) {
                ii++
                row = res[ii]
              } else {
                ii = 0
                pageRef++
                console.log(`page ${pageRef}`)
                res = await db.exec(sp, {
                  ...param,
                  _PageRef: pageRef,
                  _CountRef: 50000,
                })
                if (res.length === 50000) row = res[ii]
                else row = null
              }
            } while (row)
          } else {
            this.GenerateHeader(sheet, keys)
            do {
              let rcol_arr = []
              for (let key in row) {
                // if(is_numeric(row[key]) && strlen(row[key]) < 15)
                //     array_push($rcol_arr, ['value'=>floatval($value), 'style'=>['NumFormat'=>1]]);
                // else
                rcol_arr.push(row[key])
              }
              sheet.addRow(rcol_arr).commit() // body
              if (ii < res.length) {
                ii++
                row = res[ii]
              } else if (hasPagination) {
                console.log(2)
                ii = 0
                pageRef++
                console.log(`page ${pageRef}: ${sp}`)
                res = await db.exec(sp, {
                  ...param,
                  _PageRef: pageRef,
                  _CountRef: 50000,
                })
                if (res.length === 50000 && hasPagination) row = res[ii]
                else row = null
              } else {
                row = null
              }
            } while (row)
          }
          sheet.commit()
          console.log('done')
        }
      }
    } catch (ex) {
      console.error(ex)
    }
  },
}

router.post('/GetParamsVue', async function(req, res) {
  var dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.body.sp}' AND PARAMETER_MODE = 'IN'`
  )

  let sb = []
  for (let idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME //.substr(1);
    if (param_name.substr(-3) !== 'Ref') {
      var o = {}
      o['id'] = param_name
      if (param_name.substr(-2) == 'ID')
        //sb += '"Name":"' + param_name.substr(8, param_name.length - 10) + '",';
        o['text'] = param_name.substr(8, param_name.length - 10)
      // sb += '"Name":"' + param_name.substr(1) + '",';
      else o['text'] = param_name.substr(1)
      if (param_name.substr(-2) === 'ID') {
        // sb +=
        //   '"url":"api/call/' +
        //   param_name.substr(1, param_name.length - 3) +
        //   '",';
        o['dbref'] = param_name.substr(1, param_name.length - 3)
        //db += '"Type":"Select",';
        o['type'] = 'Select'
      } else if (
        dbp[idx].DATA_TYPE == 'datetime' ||
        dbp[idx].DATA_TYPE == 'varchar'
      ) {
        // db += '"Type":"Input",';
        o['type'] = 'Input'
      } else if (
        dbp[idx].DATA_TYPE == 'datetime' ||
        dbp[idx].DATA_TYPE == 'date'
      ) {
        // db += '"Type":"Date",';
        o['type'] = 'Date'
      } else if (
        dbp[idx].DATA_TYPE == 'boolean' ||
        dbp[idx].DATA_TYPE == 'tinyint'
      ) {
        // db += '"Type":"Checkbox",';
        o['type'] = 'Checkbox'
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"'
      }
      // db += "},";
      sb.push(o)
    }
  }
  // sb += "{";
  // sb += '"Id":"btnReport",';
  // sb += '"Name":"",';
  // sb += '"Type":"VBtn"';
  // sb += "}";
  // sb += "]";

  res.send({
    success: true,
    data: sb,
    message: '',
    type: 'array',
  })
})

router.post('/GetParams', async function(req, res) {
  var dbp = await db.pool.query(
    `SELECT * FROM information_schema.PARAMETERS
    WHERE SPECIFIC_SCHEMA = '${db.dbname}'
    AND SPECIFIC_NAME = '${req.query.sp}' AND PARAMETER_MODE = 'IN'`
  )

  let sb = '['
  for (let idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME //.substr(1);
    if (param_name.substr(-3) !== 'Ref') {
      sb += '{'
      sb += '"Id":"' + param_name + '",'
      if (param_name.substr(-2) == 'ID')
        sb += '"Name":"' + param_name.substr(8, param_name.length - 10) + '",'
      else sb += '"Name":"' + param_name.substr(1) + '",'
      if (param_name.substr(-2) === 'ID') {
        sb +=
          '"url":"api/call/' +
          param_name.substr(1, param_name.length - 3) +
          '",'
        db += '"Type":"select",'
      } else if (
        dbp[idx].DATA_TYPE == 'datetime' ||
        dbp[idx].DATA_TYPE == 'varchar'
      ) {
        db += '"Type":"text",'
      } else if (
        dbp[idx].DATA_TYPE == 'datetime' ||
        dbp[idx].DATA_TYPE == 'date'
      ) {
        db += '"Type":"date",'
      } else if (
        dbp[idx].DATA_TYPE == 'boolean' ||
        dbp[idx].DATA_TYPE == 'tinyint'
      ) {
        db += '"Type":"checkbox",'
      } else {
        db += '"Type":"' + dbp[idx].DATA_TYPE + '"'
      }
      db += '},'
    }
  }
  sb += '{'
  sb += '"Id":"btnReport",'
  sb += '"Name":"",'
  sb += '"Type":"button"'
  sb += '}'
  sb += ']'

  res.send({
    Success: true,
    Data: sb,
    Message: '',
    Type: 'array',
  })
})

router.post('/Generic/:type', async function(req, res) {
  req.setTimeout(600000)
  // let rid = req.query.rid.split("/");
  await report.Generic(req, res, req.params.type)
})

router.get('/get/:file', async function(req, res) {
  res.sendFile(path.resolve('tmp/' + req.params.file))
})

router.get('/get/templates/:file', async function(req, res) {
  res.sendFile(path.resolve('tmp/templates/' + req.params.file))
})

router.post('/template/:file', async function(req, res) {
  let data = await db.exec(req.body.sp, req.body)
  // console.log(data);
  if (req.body.renderEngine === 'text') {
    let template = fs.readFileSync('tmp/templates/' + req.params.file, 'utf-8')
    let result = ''
    for (let d of data) {
      let txt = template
      for (let c in d) {
        txt = txt.replace(new RegExp(`{{${c}}}`), d[c] || '')
      }
      result += txt
    }
    fs.writeFileSync('tmp/' + req.params.file, result)
    res.send({
      success: true,
      data: `/get/${req.params.file}`,
      message: 'Report Generated',
      type: 'url',
    })
  } else {
    carbone.render('tmp/templates/' + req.params.file, data, function(
      err,
      result
    ) {
      if (err) {
        return console.error(err)
      }
      // write the result
      fs.writeFileSync('tmp/' + req.params.file, result)
      res.send({
        success: true,
        data: `/get/${req.params.file}`,
        message: 'Report Generated',
        type: 'url',
      })
    })
  }
})

router.post('/generate/:type', async function(req, res) {
  try {
    if (req.query.sp || req.body.sp) {
      await report.Generic(req, res, req.params.type)
    } else {
      res.send({
        success: true,
        data: null,
        message: 'Cant Generate Report',
        type: 'error',
      })
    }
  } catch (ex) {
    res.send({
      success: false,
      data: ex.message,
      message: 'Error while generating report',
      type: 'error',
    })
  }
})

router.post('/custom/:rpt', async function(req, res) {
  if (req.params.rpt) {
    console.log(req.body)
    let filename = await Reporter.Render(custom[req.params.rpt], req.body)
    res.send({
      success: true,
      data: `/get/${filename}.xlsx`,
      message: 'Report Generated',
      type: 'url',
    })
  } else {
    res.send({success: false})
  }
})

module.exports = router
