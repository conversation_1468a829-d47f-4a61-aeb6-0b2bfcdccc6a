<template>
  <Page title="Laporan Hasil Disposisi" :sidebar="true">
    <div style="background: white; overflow-x: hidden; overflow-y: auto">
      <div style="padding: 10px">
        <XSelect
          placeholder="Tujuan Disposisi"
          dbref="DOC.SelPegawai"
          :dbparams="{ nocache: true }"
          :value.sync="tujuanDisposisi"
          width="100%"
          @change="PopulateSideBar"
        />
        <!-- <div class="tab-container">
          <input type="radio" name="tab" id="tab1" class="tab tab--1" />
          <label class="tab_label" for="tab1">Surat Masuk</label>

          <input type="radio" name="tab" id="tab2" class="tab tab--2" />
          <label class="tab_label" for="tab2">Surat Keluar</label>

          <div class="indicator"></div>
        </div> -->
      </div>
      <List
        :items.sync="suratList"
        @itemClick="SuratClicked"
        :selectOnLoad="true"
        style="height: calc(100vh - 220px); overflow-y: auto"
        v-show="tujuanDisposisi"
      >
        <template v-slot="{ row }">
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title style="display: flex">
                <span
                  style="
                    max-width: calc(100% - 55px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                >
                  {{ row.NoSurat }}
                </span>
                <!-- <v-icon
                  small
                  style="margin-top: -2px; margin-left: 10px"
                  color="primary"
                  v-tooltip="'Sudah Disposisi'"
                  v-show="row.Disposisi"
                >
                  mdi-account-multiple-check
                </v-icon> -->
                <v-spacer />
                <v-btn
                  x-small
                  rounded
                  v-show="row.Disposisi"
                  :outlined="!row.IsUploaded"
                  :color="row.IsUploaded ? 'primary' : ''"
                >
                  {{ row.JmlLaporan }} / {{ row.Disposisi?.split(',').length }}
                </v-btn>
              </v-list-item-title>
              <v-list-item-subtitle>
                {{ row.Keterangan }}
              </v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
        </template>
      </List>
    </div>
    <div v-show="form.SuratID">
      <div
        class="form-inline"
        style="width: 100%; max-width: 440px; padding: 0 10px"
      >
        <XInput :disabled="true" label="No Surat" :value.sync="form.NoSurat" />
        <DatePicker
          :disabled="true"
          label="Tgl Surat"
          :value.sync="form.TglSurat"
        ></DatePicker>
      </div>
      <div style="width: 100%; max-width: 440px; padding: 0 10px">
        <XInput
          :disabled="true"
          placeholder="Asal Surat"
          :value.sync="form.Asal"
        />
        <XInput
          :disabled="true"
          placeholder="Perihal"
          :value.sync="form.Perihal"
        />
        <TextArea
          :disabled="true"
          placeholder="Keterangan Acara"
          :value.sync="form.Keterangan"
          width="340px"
          height="120px"
        />
        <TextArea
          :disabled="true"
          placeholder="Catatan Kasie"
          :value.sync="form.Catatan"
          width="340px"
          height="120px"
        />
        Surat:
        <Uploader
          :disabled="true"
          mode="form"
          :value.sync="form.SuratUrl"
          accept="application/pdf"
        />
        <br />
        Hasil Laporan:
        <Uploader
          mode="form"
          :value.sync="form.LaporanUrl"
          accept="application/pdf"
          @change="SaveLaporan"
        />
      </div>
      <!-- <object
        :data="fileUrl"
        type="application/pdf"
        width="100%"
        height="1200px"
      >
        <p>
          File tidak ditemukan, silahkan upload ulang <br />
          {{ this.$api.url + this.form.SuratUrl }}
        </p>
      </object> -->
      <!-- <iframe
        :src="this.$api.url + this.form.SuratUrl"
        width="100%"
        height="80%"
      >
      </iframe> -->
    </div>
  </Page>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data: () => ({
    suratList: [],
    pegawai: [],
    tujuanDisposisi: null,
    disposisi: [],
    form: {},
    jenisSurat: 'masuk',
    keyword: '',
  }),
  computed: {
    fileUrl() {
      return this.$api.url + this.form.SuratUrl
    },
  },
  created() {
    const id = this.$route.query.id
    if (id) {
      // this.Populate(id)
      // this.PopulatePegawai(id)
      // this.setPageFocused(true)
    }
  },
  mounted() {
    this.PopulateSideBar()
    this.PopulatePegawai(0)
  },
  methods: {
    ...mapActions(['setPageFocused']),
    SuratClicked(row) {
      this.disposisi = []
      this.form = row
      this.PopulatePegawai(row.SuratID)
      this.setPageFocused(true)
    },
    async Delete() {
      if (!confirm('Anda yakin akan menghapus data ini?')) return

      let ret = await this.$api.call('DOC.DelSuratMasuk', {
        SuratID: this.form.SuratID,
      })
      if (ret.success) {
        this.PopulateSideBar()
        this.form = {}
      }
    },
    async Populate(id) {
      let surat = await this.$api.call('DOC.SelSuratMasuk', { SuratID: id })
      this.form = surat.data[0]
    },
    async PopulateSideBar() {
      let surats = await this.$api.call('DOC.SelSuratMasuk', {
        Disposisi: this.tujuanDisposisi,
      })
      // console.log(surats.data)
      this.suratList = surats.data
      // console.log('suratList', this.suratList)
    },
    async PopulatePegawai(SuratID) {
      this.pegawai = []
      let ps = await this.$api.call('DOC.SelPegawai', { SuratID })
      this.pegawai = ps.data
    },
    async SaveLaporan() {
      let ret = await this.$api.call('DOC.SavHasilLaporan', {
        UserID: this.tujuanDisposisi,
        SuratID: this.form.SuratID,
        LaporanUrl: this.form.LaporanUrl,
      })
      if (ret.success) {
        this.PopulateSideBar()
      }
    },
    async Save() {
      this.form.Disposisi = this.pegawai
        .filter((p) => p.IsChecked)
        .map((p) => p.PegawaiID)
        .join(',')
      let ret = await this.$api.call('DOC.SavSuratMasuk', this.form)
      if (!this.form.SuratID) {
        this.$api.post('/api/docs/notify-new', {
          SuratID: ret.data?.[0]?.SuratID,
          // ...this.form,
        })
      }
      if (ret.success) {
        this.PopulateSideBar()
      }
    },
    TambahSurat() {
      this.form = {}
      this.PopulatePegawai(0)
      this.setPageFocused(true)
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
.rowgrp {
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  font-size: small;
  padding: 5px;
  background: #f3f3f3;
}
.page-laporan-hasil-disposisi {
  .--input,
  textarea,
  .ui-upload {
    width: 100% !important;
  }
}

/* From Uiverse.io by zanina-yassine */
/* Remove this container when use*/
.component-title {
  width: 100%;
  position: absolute;
  z-index: 999;
  top: 30px;
  left: 0;
  padding: 0;
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #888;
  text-align: center;
}

.tab-container {
  position: relative;

  display: flex;
  flex-direction: row;
  align-items: flex-start;

  padding: 2px;

  background-color: #dadadb;
  border-radius: 9px;
}

.indicator {
  content: '';
  width: 50%;
  height: 28px;
  background: #ffffff;
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: 9;
  border: 0.5px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.12), 0px 3px 1px rgba(0, 0, 0, 0.04);
  border-radius: 7px;
  transition: all 0.2s ease-out;
}

.tab {
  width: 50%;
  height: 28px;
  position: absolute;
  z-index: 99;
  outline: none;
  opacity: 0;
}

.tab_label {
  width: 50%;
  height: 28px;

  position: relative;
  z-index: 999;

  display: flex;
  align-items: center;
  justify-content: center;

  border: 0;

  font-size: 0.75rem;
  opacity: 0.6;

  cursor: pointer;
}

.tab--1:checked ~ .indicator {
  left: 2px;
}

.tab--2:checked ~ .indicator {
  left: calc(50% - 2px);
}

.tab--3:checked ~ .indicator {
  left: calc(50% * 2 - 10px);
}
</style>
