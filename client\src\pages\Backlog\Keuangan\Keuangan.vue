<template>
  <Page title="Verifikasi Pencairan Backlog" :sidebar="true">
    <template v-slot:toolbar>
      <!-- <v-icon @click="doPrint++" v-tooltip="'Download Excel'">
        mdi-microsoft-excel
      </v-icon> -->
    </template>
    <Sidebar
      :value.sync="area"
      :rebind="rebindSidebar"
      :tabs="[2, 4]"
      :filter="filterArea"
    />
    <div
      style="padding: 10px; width: calc(100vw - 340px)"
      v-show="area.Kelurahan"
    >
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      >
        <div>
          <!-- <v-btn text small color="primary" @click="Download">
            <v-icon left>print</v-icon>
            RPD
          </v-btn> -->
          <v-btn
            small
            color="warning"
            @click="OpenRAB"
            style="margin-left: 5px"
          >
            <v-icon left>mdi-shape-plus</v-icon>
            RAB
          </v-btn>
        </div>
      </DesaGlance>
      <Grid
        id="tbl-grid"
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :filter="filterGrid"
        :autopaging="false"
        :disabled="true"
        :mobile="true"
        :doPrint="doPrint"
        :doRebind="doRebind"
        :requires="['Kabupaten']"
        height="calc(100vh - 260px); width:100%"
        :columns="[
          {
            name: 'No',
            value: 'NoRPD',
            width: '40px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain left',
            mobilePos: 'top-left',
            width: '175px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'PPHP',
            value: 'Role',
            width: '50px',
            hide: rowMode !== 'standard',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
            mobilePos: 'bottom-left',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
            hide: rowMode !== 'standard',
          },
          {
            name: 'Termin & LPJ',
            value: 'Dokumen',
            class: 'plain center',
            mobilePos: 'bottom-right',
            width: '160px',
            hide: rowMode !== 'standard',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox :value.sync="row.IsSelected" checkedIcon="check_box" />
        </template>
        <template v-slot:row-Tahapan="{ row }">
          <v-icon v-tooltip="row.Tahapan">
            mdi-numeric-{{
              row.Tahapan ? row.Tahapan.replace('REKOM ', '') : ''
            }}-box
          </v-icon>
        </template>
        <template v-slot:row-NIK="{ row }">
          <v-btn
            text
            small
            color="primary"
            @click.stop="OpenDetail(row.NoRef)"
            style="width: 165px; text-align: left"
          >
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
          <v-icon
            v-if="IsRekom"
            color="red"
            v-tooltip="'Ganti Penerima'"
            @click.stop="OpenGantiPenerima(row.NIK)"
            >mdi-account-switch-outline</v-icon
          >
        </template>
        <template v-slot:row-Role="{ row }">
          <v-icon
            :color="row.Panitia ? 'success' : '#ddd'"
            v-on="on"
            v-tooltip="row.Panitia || '-'"
          >
            {{ panitiaIcon[row.Panitia] || 'mdi-circle-outline' }}
          </v-icon>
        </template>
        <template v-slot:row-Dokumen="{ row }">
          &nbsp;
          <v-btn small outlined color="success" @click.stop="OpenChecklist">
            TR #1
          </v-btn>
          <v-btn
            small
            outlined
            color="success"
            @click.stop="OpenChecklist23(row.NIK, 2)"
            style="margin-left: 5px"
          >
            TR #2
          </v-btn>
          <v-btn
            small
            outlined
            color="success"
            @click.stop="OpenChecklist23(row.NIK, 3)"
            style="margin-left: 5px"
          >
            TR #3
          </v-btn>
          <v-btn
            small
            outlined
            color="success"
            @click.stop="OpenMonev(row.NIK)"
            style="margin-left: 5px"
          >
            MONEV
          </v-btn>
        </template>
      </Grid>
      <TextArea
        placeholder="Keterangan"
        width="1098px"
        height="200px"
        :value.sync="form.Keterangan"
      ></TextArea>
      <div style="margin-top: 10px; margin-bottom: 10px">
        <v-btn color="primary" class="mr-4" @click="Approve(1)">
          <v-icon left>mdi-check</v-icon>
          SETUJUI
        </v-btn>
        <v-btn color="error" @click="Approve(1)">
          <v-icon left>mdi-close</v-icon>
          TOLAK
        </v-btn>
      </div>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ProposalDetail :nik="selectedNIK" :show.sync="showProposalModal" />
      <ChecklistPencairan
        :kodedagri="kodedagri"
        :tahun="tahun"
        :show.sync="showChecklistModal"
      />
      <ChecklistPencairan23
        :nik="selectedNIK"
        :kodedagri="kodedagri"
        :termin="termin"
        :tahun="tahun"
        :show.sync="showChecklist23Modal"
      />
      <!-- <DataUmum
        :tahun="tahun"
        :kodeDagri="kodedagri"
        :show.sync="showDataUmum"
      /> -->
      <RAB :show.sync="showRAB" :kodeDagri="kodedagri" :tahun="tahun" />
      <ChecklistMonev :nik="selectedNIK" :show.sync="showChecklistMonev" />
    </div>
  </Page>
</template>
<script>
import Sidebar from '../Pencairan/SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import ChecklistPencairan from '../Pencairan/Checklist.vue'
import ChecklistPencairan23 from '../Pencairan/Checklist23.vue'
import ChecklistMonev from '../LPJ/Checklist.vue'
// import DataUmum from '../Pencairan/DataUmum.vue'
import RAB from '../InputUsulan/RAB.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ChecklistPencairan,
    ChecklistPencairan23,
    ChecklistMonev,
    // DataUmum,
    RAB,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    area: {},
    form: {},
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showChecklist23Modal: false,
    showChecklistMonev: false,
    showRAB: false,
    termin: 2,
    showDataUmum: false,
    selectedRef: null,
    selectedNIK: null,
    kodedagri: null,
    tahun: null,
    doPrint: 0,
    doRebind: 0,
    panitiaIcon: {
      Ketua: 'mdi-crown-circle-outline',
      Sekretaris: 'mdi-pencil-circle-outline',
      Anggota: 'mdi-account-circle-outline',
    },
    rebindSidebar: 0,
    filterArea: {
      IsApproved: 1,
    },
    rowMode: 'standard',
    IsRekom: false,
    NoRefs: ',',
  }),
  computed: {
    dbref() {
      // if (this.area.tabId == 2) {
      //   return 'PRM.ProposalDet'
      // } else {
      //   return 'PRM.ProposalBSPS'
      // }
      return 'BLG.ProposalDet'
    },
    areaParams() {
      let { Kabupaten, Kecamatan, Kelurahan } = this.area
      return { Kabupaten, Kecamatan, Kelurahan }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue
      }).length
    },
    buttonStatus() {
      return this.datagrid.reduce((total, curr) => {
        if (curr.IsSelected && curr.Tahapan) total['IsCancel'] = true
        if (curr.IsSelected && !curr.Tahapan) total['IsRekom'] = true
        return total
      }, {})
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    datagrid() {
      this.Populate()
    },
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenChecklist() {
      this.kodedagri = this.area.KodeDagri
      this.tahun = this.area.Tahun
      this.showChecklistModal = true
    },
    OpenChecklist23(nik, termin) {
      this.selectedNIK = nik
      this.kodedagri = this.area.KodeDagri
      this.termin = termin
      this.tahun = this.area.Tahun
      this.showChecklist23Modal = true
    },
    OpenMonev(nik) {
      this.selectedNIK = nik
      this.showChecklistMonev = true
    },
    OpenDataUmum() {
      this.kodedagri = this.area.KodeDagri
      this.tahun = this.area.Tahun
      this.showDataUmum = true
    },
    OpenRAB() {
      this.kodedagri = this.area.KodeDagri
      this.tahun = this.area.Tahun
      this.showRAB = true
    },
    filterGrid(row) {
      return Boolean(row.IsApproved)
    },
    async ChangeRole(txt, row) {
      let ret = await this.Update('Panitia', row.NIK, txt == '-' ? null : txt)
      if (ret.success) row.Panitia = txt == '-' ? null : txt
    },
    async Update(col, nik, val) {
      // console.log(col, nik, val)
      return await this.$api.call('BLG_UpdProposalDet', {
        Column: col,
        Value: val,
        NIK: nik,
      })
    },
    Download() {
      window.open(
        this.$api.url +
          '/report/backlog/doc/RPD.ods?out=xlsx&Tahun=' +
          this.area.Tahun +
          '&KelurahanID=' +
          this.area.KelurahanID
      )
    },
    async Populate() {
      let { data } = await this.$api.call('BLG.SelFinApproval', {
        Tahun: this.area.Tahun,
        KodeDagri: this.area.KodeDagri,
      })
      if (data?.length) {
        this.form = data[0]
      } else {
        this.form = {}
      }
    },
    async Approve(status) {
      let ret = await this.$api.call('BLG.SavFinApproval', {
        Tahun: this.area.Tahun,
        KodeDagri: this.area.KodeDagri,
        Status: status,
        Keterangan: this.form.Keterangan,
      })
      // this.dbparams.isRekom = { ...this.dbparams, isRekom: rekom };
      if (ret.success) {
        this.rebindSidebar++
      }
    },
  },
}
</script>
<style lang="scss">
.page-pencairan-backlog {
  #tbl-grid {
    max-width: 100%;
    width: 1126px;
  }
}
</style>
