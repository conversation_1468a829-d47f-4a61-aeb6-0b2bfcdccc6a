// Load environment variables
require('dotenv').config();
const axios = require('axios');

// console.log(process.env) // Commented out for production
const fastify = require('fastify')({
    logger: true
});

// Register CORS plugin
fastify.register(require('@fastify/cors'), {
    origin: true // Allow all origins, configure as needed
});

// Register body parser for JSON (built into Fastify 4+)
// No need to explicitly register, it's included by default

// Add telegram service to fastify instance (placeholder)
// You can replace this with actual telegram service implementation
fastify.decorate('telegram', {
    sendAdmin: async (message) => {
        console.log('Telegram Admin Message:', message);
        const TELEGRAM_TOKEN = process.env.TELEGRAM_TOKEN;
        const TELEGRAM_ADMIN = process.env.TELEGRAM_ADMIN;

        if (!TELEGRAM_TOKEN || !TELEGRAM_ADMIN) {
            console.error('Telegram token or admin chat ID not set in environment variables.');
            return;
        }

        const url = `https://api.telegram.org/bot${TELEGRAM_TOKEN}/sendMessage`;
        try {
            await axios.post(url, {
                chat_id: TELEGRAM_ADMIN,
                text: message
            });
            console.log('Telegram message sent successfully to admin.');
        } catch (error) {
            console.error('Error sending Telegram message:', error.response ? error.response.data : error.message);
        }
    }
});

// Register authentication plugin (includes cookie support)
const { authPlugin } = require('./api/auth');
fastify.register(authPlugin);

// Register Server-Sent Events plugin
const eventsModule = require('./api/events');
fastify.register(eventsModule.plugin);

// Register the call routes plugin
fastify.register(require('./api/call'), { prefix: '/api' });

// Health check route
fastify.get('/health', async (request, reply) => {
    return { status: 'ok', timestamp: new Date().toISOString() };
});

// Start the server
const start = async () => {
    try {
        const port = process.env.PORT || 3000;
        const host = process.env.HOST || '0.0.0.0';

        await fastify.listen({ port, host });
        console.log(`Server is running on http://${host}:${port}`);
    } catch (err) {
        fastify.log.error(err);
        process.exit(1);
    }
};

start();