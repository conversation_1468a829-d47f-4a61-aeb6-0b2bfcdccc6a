<template>
  <Page title="Input Berkas Penyaluran" :sidebar="true">
    <template v-slot:toolbar>
      <!-- <v-icon @click="doPrint++" v-tooltip="'Download Excel'">
        mdi-microsoft-excel
      </v-icon> -->
    </template>
    <Sidebar :tabs="[2]" :value.sync="area" />
    <div style="padding: 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        :isApproved.sync="isApproved"
        :hideDetail="true"
        style="margin-bottom: 8px"
      />
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :disabled="true"
        height="calc(100vh - 230px)"
        :doRebind="doRebind"
        :doPrint="doPrint"
        groupBy="Tahapan"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            filter: {
              type: 'search',
              value: 'NIK',
            },
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'plain fix-width',
            filter: {
              type: 'search',
              value: 'Nama',
            },
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
            filter: {
              type: 'search',
            },
          },
          {
            name: 'Kabupaten',
            value: 'Kabupaten',
            hide: true,
          },
          {
            name: 'Kecamatan',
            value: 'Kecamatan',
            hide: true,
          },
          {
            name: 'Kelurahan',
            value: 'Kelurahan',
            hide: true,
          },
          {
            name: 'Kekurangan',
            value: 'Kekurangan',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox
            :value.sync="row.CheckedValue"
            checkedIcon="check_box"
            disabledIcon="mdi-lock"
            :disabled="Boolean(row.IsLocked)"
            @click="SubmitProposal(row.NIK, ...arguments)"
          />
        </template>
        <template v-slot:row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template v-slot:row-KRT_Nama="{ row }">
          <v-btn text small color="primary" @click="OpenDetail(row.NoRef)">
            {{ row.KRT_Nama }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
            >mdi-account-check</v-icon
          >
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-Kekurangan="{ row }">
          <v-icon
            v-if="row.Kekurangan"
            color="warning"
            v-tooltip="row.Kekurangan"
          >
            mdi-alert
          </v-icon>
        </template>
        <template v-slot:group-row="{ row, columns }">
          <td
            class="group-row"
            :colspan="columns.length"
            style="background: #eee"
          >
            <div style="display: flex">
              <div style="font-weight: bold; font-size: 14px">
                {{ row.Tahapan || 'Belum Direkom' }}
              </div>
              <rekom-block
                :rekom="row.Tahapan?.replace('REKOM', '')"
                v-show="false"
              />
              <v-spacer />
              <div v-if="row.Kekurangan">
                <v-btn x-small text outlined color="warning">
                  {{ row.Kekurangan }}
                </v-btn>
              </div>
              <div v-else>
                <v-btn
                  x-small
                  :outlined="!row.IsComplete"
                  :color="row.IsComplete ? 'success' : 'primary'"
                  @click="OpenProposal(row.Tahapan, row.KodeWilayah)"
                >
                  upload berkas
                </v-btn>
                <v-btn
                  x-small
                  text
                  color="primary"
                  v-show="false"
                  @click="$router.push('/Main/RTLH/RekomProposal/')"
                >
                  halaman rekom
                </v-btn>
              </div>
            </div>
          </td>
        </template>
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
        @save="doRebind++"
      />
      <ProposalDetail
        :show.sync="showProposalModal"
        :param="selected"
        @save="doRebind++"
      />
      <Messages
        :tahun="area.Tahun"
        :noRef="selectedRef"
        :show.sync="showMessages"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import ProposalDetail from './ProposalDetail.vue'
import Messages from '../ReviewUsulan/Messages.vue'
import RekomBlock from '../../../components/RekomBlock.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    Messages,
    RekomBlock,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showMessages: false,
    selectedRef: null,
    doPrint: 0,
    doRebind: 0,
    selected: {
      Tahun: null,
      Tahapan: null,
      KodeDagri: null,
    },
    isApproved: null,
  }),
  computed: {
    dbref() {
      if ([2, 12].includes(this.area.tabId) || !this.area.tabId) {
        return 'PRM.BerkasDet'
      } else {
        return 'PRM.ProposalBSPS'
      }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue && (d.FinalStatus || 'OK') == 'OK'
      }).length
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    isApproved(val) {
      this.area = { ...this.area, isApproved: val }
    },
    '$route.query'() {
      this.area = this.$route.query
    },
  },
  mounted() {
    if (this.$route.query) this.area = this.$route.query
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },

    /**
     * Membuka Pop-Up Proposal
     * @param {string} Tahapan - tahapan rekomendasi (REKOM 1, REKOM 2, dst)
     * @param {string} KodeDagri - kode desa (kemendagri)
     */
    OpenProposal(Tahapan, KodeDagri) {
      this.selected.Tahun = this.area.Tahun
      this.selected.Tahapan = Tahapan
      this.selected.KodeDagri = KodeDagri
      this.showProposalModal = true
    },

    OpenMessages(noRef) {
      this.selectedRef = noRef
      this.showMessages = true
    },
    async SubmitProposal(nik, checked, callback) {
      var ret = await this.$api.call('PRM.SavProposalDetByNIK', {
        ProposalID: this.area.ProposalID,
        NIK: nik,
        IsAdd: checked,
        Sumber: this.area.tabId,
      })
      if (!ret.success) {
        callback(false)
      } else {
        if (this.datagrid.length == 1) this.doRebind++
      }
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
