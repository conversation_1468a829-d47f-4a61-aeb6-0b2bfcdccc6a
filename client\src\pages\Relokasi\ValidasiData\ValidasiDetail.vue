<template>
  <Modal
    title="VALIDASI DETAIL"
    :show.sync="x_show"
    :loading="loading"
    :error="error"
    :showActions="!disabled"
    @onSubmit="Save"
  >
    <template v-slot:left-action>
      <v-btn text color="red" @click="Delete">Hapus</v-btn>
    </template>
    <div
      style="display: flex"
      :style="isMobile ? 'width:100vw;' : 'width:700px; height:500px;'"
      :class="isMobile ? '' : 'form-inline'"
    >
      <div style="width: 50px; color: #333" v-show="!isMobile">
        <div
          class="tab"
          @click="tabId = 'Individu'"
          :class="{
            active: tabId == 'Individu',
          }"
        >
          <v-icon style="font-size: 36px">person</v-icon>
        </div>
        <div
          tab="Rumah"
          class="tab"
          @click="tabId = 'Rumah'"
          :class="{
            active: tabId == 'Rumah',
          }"
        >
          <v-icon style="font-size: 36px">home</v-icon>
        </div>
        <div
          tab="Foto"
          class="tab"
          @click="tabId = 'Foto'"
          :class="{
            active: tabId == 'Foto',
          }"
        >
          <v-icon style="font-size: 36px">collections</v-icon>
        </div>
      </div>
      <div
        style="overflow: auto; border-left: 1px solid silver; margin-left: 8px"
        :style="isMobile ? 'width:100vw;' : 'width:700px; height:500px;'"
      >
        <div v-show="tabId == 'Individu' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XLabel
              label="IDBDT"
              :value="
                forms.TipeData === 115
                  ? '(BDT 2015)'
                  : forms.IDBDT || '(INPUT MANUAL)'
              "
            />
            <XInput label="NIK*" :value.sync="forms.NIK">
              <template v-slot:label>
                <div style="display: flex">
                  <div>NIK*</div>
                  <v-spacer />
                  <v-btn
                    x-small
                    :outlined="forms.TipeData < 200 || isBDT"
                    color="primary"
                    style="margin-top: 2px; margin-right: 8px"
                    @click="CheckDinsos"
                  >
                    {{ 200 > forms.TipeData || isBDT ? 'bdt' : 'check bdt' }}
                  </v-btn>
                </div>
              </template>
            </XInput>
            <XInput label="No. KK" :value.sync="forms.NoKK" />
            <XInput label="Nama" :value.sync="forms.Nama" />
            <XInput label="Alamat" :value.sync="forms.Alamat" />
            <XInput label="Umur" :value.sync="forms.Umur" />
            <XSelect
              label="Jns. Kelamin"
              :value.sync="forms.JenisKelamin"
              :items="[
                { val: 'L', txt: 'Laki-Laki' },
                { val: 'P', txt: 'Perempuan' },
              ]"
            />
            <XSelect
              label="Sts. Perkawinan*"
              :value.sync="forms.Perkawinan"
              dbref="PRM.SelPerkawinan"
            />
            <XSelect
              label="Pendidikan"
              :value.sync="forms.Pendidikan"
              dbref="PRM.SelPendidikan"
            />
            <XSelect
              label="Pekerjaan"
              :value.sync="forms.Pekerjaan"
              dbref="PRM.SelPekerjaan"
            />
            <XSelect
              label="Penghasilan*"
              :value.sync="forms.Penghasilan"
              dbref="PRM.SelPenghasilan"
            />
            <XInput label="NPWP" :value.sync="forms.NoNPWP" />
            <XSelect
              label="Mampu Swadaya"
              :value.sync="forms.MampuSwadaya"
              :items="[
                { val: '1', txt: 'Mampu' },
                { val: '0', txt: 'Tidak Mampu' },
              ]"
            />
            <XSelect
              label="Nilai Swadaya"
              :value.sync="forms.NilaiSwadaya"
              :items="[
                { val: 1, txt: '< 5jt' },
                { val: 6, txt: '> 5jt' },
              ]"
            />
          </div>
        </div>
        <div v-show="tabId == 'Rumah' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              label="Sts. Rumah"
              :value.sync="forms.KepemilikanRumah"
              :items="[
                { val: 1, txt: 'Milik Sendiri' },
                { val: 2, txt: 'Kontrak/Sewa' },
                { val: 3, txt: 'Bebas Sewa' },
                { val: 4, txt: 'Dinas' },
                { val: 6, txt: 'Menumpang' },
                { val: 5, txt: 'Lainnya' },
              ]"
            />
            <XSelect
              label="Memiliki Tanah*"
              :value.sync="forms.TanahLain"
              :items="[
                { val: 0, txt: 'Tidak Memiliki' },
                { val: 1, txt: 'Memiliki' },
              ]"
            />
            <XSelect
              label="Sertifikat Tanah*"
              :value.sync="forms.KepemilikanLahan"
              dbref="PRM.SelStatusTanah"
            />
            <XSelect
              label="Asal Tanah"
              :value.sync="forms.KondisiLantai"
              dbref="RLK.SelAsalTanah"
            />
            <XSelect
              v-show="forms.KondisiLantai != 1"
              label="Hub. dg Pemilik"
              :value.sync="forms.LantaiID"
              dbref="RLK.SelHubunganPemilik"
            />
            <XInput label="Alamat Tanah" :value.sync="forms.AlamatTanahLain" />
            <XLabel label="Ukuran Tanah*">
              <XInput
                type="number"
                placeholder="Panjang"
                width="81px"
                :value.sync="forms.PanjangTanah"
              />
              <v-icon style="font-size: 18px; position: relative; top: -2px"
                >clear</v-icon
              >
              <XInput
                type="number"
                placeholder="Lebar"
                width="81px"
                :value.sync="forms.LebarTanah"
              />
            </XLabel>
            <XInput
              label="Jml. Keluarga*"
              type="number"
              :value.sync="forms.JmlKK"
            />
            <XInput
              label="Jml. Penghuni*"
              type="number"
              :value.sync="forms.JmlPenghuni"
            />
            <XLabel label="Sumber Bantuan">
              <XSelect
                :value.sync="forms.Sumber"
                dbref="PRM.SelSumber"
                style="margin-right: 10px"
              />
              <XSelect
                :items="limaTahun"
                :value.sync="forms.Tahun"
                placeholder="Tahun"
                width="80px"
              />
            </XLabel>

            <XSelect
              label="Kawasan"
              :value.sync="forms.KawasanPerumahan"
              dbref="PRM.SelKawasan"
            />
          </div>
        </div>
        <div v-show="tabId == 'Foto' || isMobile">
          <div style="padding-left: 10px; display: flex">
            <div>
              <XInput
                label="Latitude"
                :value.sync="forms.GeoLat"
                v-if="isMobile"
              />
              <XInput
                label="Longitude"
                :value.sync="forms.GeoLng"
                v-if="isMobile"
              />
              <div v-if="!isMobile">
                <Map
                  :disabled="disableMap || disabled"
                  :ref="'map'"
                  :lat.sync="forms.GeoLat"
                  :lon.sync="forms.GeoLng"
                  width="600px"
                  height="350px"
                  :geojson="geojson"
                  @change="onMapChange"
                />
              </div>
              <Uploader
                label="Foto Calon Lahan"
                :value.sync="forms.Rumah0"
                @change="updateMapLocation"
                accept=".jpg,.jpeg,.jfif,.png,.heic"
              >
              </Uploader>
              <!-- KondisiLantai = Asal Tanah -->
              <Uploader
                v-show="forms.KondisiLantai != 1"
                label="Bukti Pindah Tangan"
                :value.sync="forms.PindahTangan"
                accept=".jpg,.jpeg,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="Foto Penerima"
                :value.sync="forms.Profile"
                accept=".jpg,.jpeg,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="Berkas Verval"
                :value.sync="forms.SuratKesanggupan"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.pdf"
              >
              </Uploader>
              <Uploader
                label="Foto KTP"
                :value.sync="forms.KTP"
                accept=".jpg,.jpeg,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="Foto KK"
                :value.sync="forms.KartuKeluarga"
                accept=".jpg,.jpeg,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="Foto KK Pemilik"
                :value.sync="forms.KKPemilik"
                accept=".jpg,.jpeg,.jfif,.png,.heic"
              >
              </Uploader>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
import moment from 'moment'
import { fromLonLat } from 'ol/proj'

export default {
  data: () => ({
    x_show: false,
    loading: false,
    error: '',
    isBDT: false,
    geojson: null,
    disableMap: false,

    forms: {},
    tabId: 'Individu',
    photoGpsTag: { lat: null, lon: null },
  }),
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    noRef: [String, Number],
    area: Object,
    disabled: Boolean,
  },
  computed: {
    limaTahun() {
      let tahun = []
      for (let i = 0; i < 5; i++) {
        tahun.push({ val: moment().year() - i, txt: moment().year() - i })
      }
      return tahun
    },
  },
  watch: {
    show(val) {
      this.x_show = val
      this.tabId = 'Individu'
      this.error = ''
      this.errorAction = ''
    },
    x_show(val) {
      if (!val) {
        this.forms.RumahDepan = ''
        this.forms.RumahSamping = ''
        this.forms = {}
        this.photoGpsTag = null
      } else this.populate()
      this.$emit('update:show', val)
    },
    'forms.NIK'(val) {
      if (val && val.length == 16) this.CheckDinsos()
      else {
        this.forms.NewTipeData = null
      }
    },
    // noRef() {
    //   this.populate()
    // },
  },
  methods: {
    async CheckDinsos() {
      if (
        this.forms.NIK &&
        (this.forms.NIK + '').length == 16 &&
        !(200 > this.forms.TipeData || this.isBDT)
      ) {
        let d = await this.$api.post(
          this.$api.url + '/api/dinsos/get/' + this.forms.NIK
        )
        if (d.success) {
          if (this.forms.TipeData > 200 && this.forms.TipeData < 300) {
            this.forms.NewTipeData -= new Date().getYear()
          }
          this.isBDT = true

          let alamat =
            (d.data.ALAMAT || d.data.alamat || '').trim() +
            ' RT ' +
            (d.data.RT || d.data.rt || '?') +
            ' RW ' +
            (d.data.RW || d.data.rw || '?')
          this.forms.Nama = (d.data.Nama || d.data.nama).trim()
          this.forms.Alamat =
            alamat.length > this.forms.Alamat ? alamat : this.forms.Alamat
          this.forms.IDBDT = d.data.IDBDT || d.data.id_dtks || d.data.idjtg
          this.forms.NIKBDT = d.data.nik || this.forms.NIK
          this.forms.NamaBDT = (d.data.Nama || d.data.nama).trim()
          this.forms.NewTipeData = new Date().getYear()
        } else {
          this.$api.notify('Belum terdaftar di BDT', 'error')
        }
      }
    },
    async populate() {
      this.loading = true
      this.isBDT = false
      if (this.noRef) {
        let { data } = await this.$api.call('PRM_SelPBDTDetail', {
          NoRef: this.noRef,
        })
        this.forms = data[0]
      } else {
        this.forms = {
          NIK: '',
          Nama: '',
          Alamat: '',
          IDBDT: '',
          NewTipeData: '',
        }
      }
      this.loading = false
    },
    async Save() {
      this.error = ''
      let ret = await this.$api.call('PRM.SavPBDTDetail', {
        Tahun: null,
        Sumber: null,
        ...this.forms,
        Kabupaten: this.area.Kabupaten,
        Kecamatan: this.area.Kecamatan,
        Kelurahan: this.area.Kelurahan,
        KodeWilayah: this.area.KodeDagri,
      })
      if (ret.success) this.x_show = false
      else this.error = ret.message
    },
    async Delete() {
      if (!confirm(`Anda yakin akan menghapus ${this.forms.Nama}?`)) return

      let ret = await this.$api.call('PRM.DelPBDTedit', {
        ...this.forms,
        Kabupaten: this.area.Kabupaten,
        Kecamatan: this.area.Kecamatan,
        Kelurahan: this.area.Kelurahan,
        KodeWilayah: this.area.KodeDagri,
      })
      if (ret.success) {
        this.x_show = false
        this.$emit('delete')
      } else this.error = ret.message
    },
    updateMapLocation(res) {
      if (res?.meta?.gps?.lat) {
        this.disableMap = true
        this.photoGpsTag = {
          lat: res.meta.gps.lat,
          lon: res.meta.gps.lon,
        }

        this.forms.GeoLat = res.meta.gps.lat
        this.forms.GeoLng = res.meta.gps.lon
      }
    },
    isInside(point, vs) {
      // ray-casting algorithm based on
      // https://wrf.ecse.rpi.edu/Research/Short_Notes/pnpoly.html

      var x = point[0],
        y = point[1]

      var inside = false
      for (var i = 0, j = vs.length - 1; i < vs.length; j = i++) {
        var xi = vs[i][0],
          yi = vs[i][1]
        var xj = vs[j][0],
          yj = vs[j][1]

        var intersect =
          yi > y != yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi
        if (intersect) inside = !inside
      }

      return inside
    },
    async onMapChange() {
      this.$refs.map.setTooltip('Loading ..')
      let zoom = this.$refs.map.mapView.getZoom()
      if (zoom < 14) this.$refs.map.mapView.setZoom(14)
      let params = {
        lat: this.forms.GeoLat,
        lon: this.forms.GeoLng,
        kab: this.forms.Kabupaten,
        kec: this.forms.Kecamatan,
      }
      let d = await this.$api.get(
        this.$api.url + '/api/gistaru?' + new URLSearchParams(params).toString()
      )
      let attr = d.data?.features?.[0]?.attributes
      if (attr) {
        this.$refs.map.setTooltip(
          `<div>${attr.NAMZON || ''}</div><div>${attr.NAMOBJ}</div>`,
          {
            style: {
              color: ['RTH', 'BJ'].includes(attr.KODZON) ? 'white' : 'black',
              backgroundColor: ['RTH', 'BJ'].includes(attr.KODZON)
                ? 'rgba(244, 57, 64, 0.8)'
                : 'rgba(255,255,255,0.7)',
            },
          }
        )
      } else {
        this.$refs.map.setTooltip('-')
      }
      let poly = d.data?.features?.[0]?.geometry?.rings.find((p) =>
        this.isInside([params.lon, params.lat], p)
      )
      this.$refs.map.removeLayer('geojson-layer')
      if (poly)
        this.geojson = {
          type: 'FeatureCollection',
          style: {
            color: ['RTH', 'BJ'].includes(attr.KODZON) ? 'red' : 'blue',
            fillColor: ['RTH', 'BJ'].includes(attr.KODZON)
              ? 'rgba(244, 57, 64, 0.8)'
              : 'rgba(255,255,255,0.1)',
          },
          features: [
            {
              type: 'Feature',
              geometry: {
                type: 'Polygon',
                coordinates: [poly.map((a) => fromLonLat(a))],
              },
            },
          ],
        }
    },
  },
}
</script>
<style lang="scss" scoped>
.tab {
  width: 50px;
  height: 50px;
  vertical-align: top;
  cursor: pointer;
  text-align: center;
  border-bottom: 1px solid silver;

  .v-icon {
    margin-top: 8px;
    color: silver;
  }
}

.tab.active {
  background: #f3f3f3;
  position: relative;
  z-index: 2;
  .v-icon {
    color: #333;
  }
}
[tabpanel='Kesehatan'] .tform {
  width: 180px;
}
.tform {
  min-width: 140px;
}
.imgbox {
  width: 150px;
  height: 150px;
  border-right: 1px solid white;
  box-sizing: border-box;
}
</style>
