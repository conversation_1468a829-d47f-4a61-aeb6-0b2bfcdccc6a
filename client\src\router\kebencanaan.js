export default [
  {
    path: '/Main/Kebencanaan/Map',
    component: () => import('../pages/Kebencanaan/Map/Database.vue'),
  },
  {
    path: '/Main/Kebencanaan/Database',
    component: () => import('../pages/Kebencanaan/Database/Database.vue'),
  },
  {
    path: '/Main/Kebencanaan/ValidasiData',
    component: () =>
      import('../pages/Kebencanaan/ValidasiData/ValidasiData.vue'),
  },
  // {
  //   path: '/Main/Kebencanaan/Dashboard',
  //   component: () => import('../pages/Kebencanaan/Dashboard/Dashboard.vue'),
  //   meta: { title: 'SIMPERUM', noauth: true },
  // },
  {
    path: '/Main/Kebencanaan/InputUsulan',
    component: () => import('../pages/Kebencanaan/InputUsulan/InputUsulan.vue'),
  },
  {
    path: '/Main/Kebencanaan/ReviewKabupaten',
    component: () =>
      import('../pages/Kebencanaan/ReviewKabupaten/ReviewKabupaten.vue'),
  },
  {
    path: '/Main/Kebencanaan/ReviewProposal',
    component: () =>
      import('../pages/Kebencanaan/ReviewUsulan/ReviewUsulan.vue'),
  },
  {
    path: '/Main/Kebencanaan/Pencairan',
    component: () => import('../pages/Kebencanaan/Pencairan/Pencairan.vue'),
  },
  {
    path: '/Main/Kebencanaan/LPJ',
    component: () => import('../pages/Kebencanaan/LPJ/LPJ.vue'),
  },
  {
    path: '/Main/Kebencanaan/SkGub',
    component: () => import('../pages/Kebencanaan/SkGub/Alokasi.vue'),
  },
  {
    path: '/Main/Kebencanaan/Monev',
    component: () => import('../pages/Kebencanaan/Monitoring/Monitoring.vue'),
  },
  {
    path: '/Main/Kebencanaan/Report',
    component: () => import('../pages/Kebencanaan/Report.vue'),
  },
]
