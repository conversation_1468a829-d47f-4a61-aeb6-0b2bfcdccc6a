<template>
  <Page title="Aloka<PERSON> Dewan">
    <div style="padding: 10px">
      <div style="display: flex">
        <XSelect
          dbref="PRM.SelProposal"
          :value.sync="proposal"
          :valueAsObject="true"
          width="80px"
          style="margin-right: 10px"
        />
        <!-- <Checkbox
          :value.sync="hasAllocation"
          style="margin-left: 10px; margin-top: 2px"
          text="Mempunyai Alokasi"
        /> -->
      </div>
      <Grid
        dbref="PRM.AlokasiDewan"
        :dbparams="params"
        :datagrid.sync="forms.XmlAlokasi"
        :filter="filterGrid"
        :disabled="true"
        :autopaging="false"
        height="calc(100vh - 240px)"
        :columns="[
          {
            name: '<PERSON><PERSON>',
            value: 'NamaDewan',
            width: '350px',
          },
          {
            name: '<PERSON><PERSON>',
            value: 'Kuota',
            class: 'plain',
          },
          {
            name: '<PERSON><PERSON><PERSON>',
            value: '<PERSON><PERSON><PERSON>',
            class: 'right',
          },
          {
            name: 'Dapil',
            value: 'AreaAccess',
            class: 'plain',
          },
          {
            name: 'Aks<PERSON>',
            value: 'IsOpen',
            class: 'plain',
          },
        ]"
      >
        <template v-slot:row-Kuota="{ row }">
          <XInput type="number" :value.sync="row.Kuota" width="80px" />
        </template>
        <template v-slot:row-AreaAccess="{ row }">
          <div center>
            <v-icon @click="ShowAreaAccess(row.UserID)">
              mdi-map-marker-path
            </v-icon>
          </div>
        </template>
        <template v-slot:row-IsOpen="{ row }">
          <div center>
            <Checkbox
              :value.sync="row.IsOpen"
              checkedIcon="mdi-lock-open-check"
              uncheckedIcon="mdi-lock"
            />
          </div>
        </template>
        <template v-slot:footer>
          <td>
            <div style="padding: 12px; font-weight: bold">Total</div>
          </td>
          <td>
            <div style="padding: 12px; font-weight: bold; text-align: right">
              {{ totalAlokasi | format }}
            </div>
          </td>
          <td>
            <div style="padding: 12px; font-weight: bold; text-align: right">
              {{ totalDipakai | format }}
            </div>
          </td>
          <td colspan="2">
            <div style="padding: 12px"></div>
          </td>
        </template>
      </Grid>
      <br />
      <v-btn color="primary" @click="Save" :loading="loading"> SIMPAN </v-btn>
    </div>
    <Modal
      id="modal-roles"
      :show.sync="showAreaAccess"
      title="DAPIL"
      @onSubmit="SubmitAreaAccess"
    >
      <AreaAccess :userId="userId" :page-data.sync="areaAccessData" />
    </Modal>
  </Page>
</template>
<script>
import AreaAccess from '@/pages/Admin/User/AreaAccess.vue'
export default {
  components: { AreaAccess },
  data: () => ({
    loading: false,
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: {
      XmlAlokasi: [],
    },
    totalAlokasi: 0,
    totalDipakai: 0,
    hasAllocation: false,
    showAreaAccess: false,
    userId: null,
  }),
  watch: {
    'forms.XmlAlokasi'(val) {
      this.totalAlokasi = val.reduce((a, b) => a + (b.Kuota || 0), 0)
      this.totalDipakai = val.reduce((a, b) => a + (b.Dipakai || 0), 0)
    },
  },
  created() {
    for (let i = 0; i < 200; i++) {
      this.forms.XmlAlokasi.push({})
    }
  },
  computed: {
    params() {
      let { InputName } = this.proposal
      return { Tahun: InputName }
    },
  },
  methods: {
    ShowAreaAccess(userId) {
      this.showAreaAccess = true
      this.userId = userId
    },
    async SubmitAreaAccess() {
      let ret = await this.$api.call('Arch.SavUserArea', {
        UserID: this.userId,
        Remarks: this.areaAccessData
          .filter((a) => a.AllowAccess)
          .map((a) => a.AreaID)
          .join('|'),
      })

      if (ret.success) this.showAreaAccess = false
    },
    async Save() {
      this.loading = true
      await this.$api.call('PRM_SavAlokasiDewan', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
      this.loading = false
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
  },
}
</script>
<style lang="scss"></style>
