const mysql = require("mysql");
const shell = require('shelljs')
const fs = require('fs')
// const moment = require('moment')

const cfgSource = {
  connectionLimit: 10,
  host: "***************",
  user: "dev_team",
  port: '3306',
  password: "x2e7fT8rNWJsChWu",
  database: "the_collective_db"
}
const dbSource = mysql.createPool(cfgSource);

var dbTarget = mysql.createPool({
  connectionLimit: 10,
  host: "*************",
  user: "dev_team",
  port: '3306',
  password: "x2e7fT8rNWJsChWu",
  database: "the_collective_db"
});

const getTables = (db) => {
  return new Promise((resolve, reject) => {
    const sql = `select table_schema as database_name,
       table_name
   from information_schema.tables
where table_type = 'BASE TABLE'
      and table_rows = 0
      and table_schema = 'the_collective_db' -- put your database name here
      and table_name not in ('alembic_version',
                          'broker_contact',
                          'cargo',
                          'contact_information_full',
                          'docked_port_polygons_ports_2022',
                          'ds_owner_email_test',
                          'forecast_data',
                          'ports_distance_new',
                          'predictive_analytics_dataset_NN',
                          'temp_vessel_position_octavian',
                          'test_position',
                          'test_position_temp',
                          'tf_cargo_flow_hf',
                          'user_change_tracking',
                          'vessel_consumption',
                          'vessel_operator',
                          'vessel_polygon_test',
                          'vessel_position_2022',
                          'vessel_position_2022_',
                          'vessel_position_2022_september',
                          'vessel_position_bkp_migration',
                          'vessel_position_docked_port',
                          'vessel_position_migration_mmsi',
                          'vessel_position_migration_mmsi_final',
                          'vessel_position_migration_mmsi_final_2',
                          'vessel_position_migration_mmsi_final_test',
                          'vessel_position_migration_mmsi_martin',
                          'vessel_position_migration_mmsi_new',
                          'vessel_position_migration_unified_bkp',
                          'vessel_position_temp',
                          'vessel_position_temp2',
                          'vessel_position_temp3',
                          'vessel_position_temp4',
                          'vessel_position_temp5',
                          'vessel_position_wspatial',
                          'vessel_position_bkp_december',
                            'vessel_bkup_09_aug_21',
                          'vessel_bkup_15_oct_21',
                          'vessel_bkup_2023',
                          'vessel_position_fill',
                          'vessel_position_load_discharge',
                          'vessel_position_logs',
                          'vessel_position_migration',
                          'vessel_position_reprocess',
                          'vessel_position_bkp',
                          'vessel_position_c5_old',
                          'vessel_position_detect_null',
                          'vessel_position_duplicates',
                          'vessel_position_laden',
                          'vessel_position_migration_unified',
                          'vessel_position_phv_aux',
                          'vessel_position_slow',
                          'vessel_position_slow2',
                          'vessel_position_sp_test',
                                      'ports_distance_path',
                          'ports_distance_path_bkp',
                          'ports_distance_path_bkp2',
                          'ports_distance_path_new',
                          'vessel_voyage_bkp',
                          'vessel_voyage_bkp1',
                          'vessel_voyage_new',
                          'vessel_voyage_new_',
                          'vessel_voyage_temp_dec')
      order by table_schema,
         table_name;`
    //     `SELECT table_name AS "Table", ROUND(((data_length + index_length) / 1024), 2) AS "Size (KB)" FROM information_schema.TABLES
    // where TABLE_SCHEMA = 'the_collective_db' and ROUND(((data_length + index_length) / 1024), 2) < 100 and TABLE_NAME > 'ports' and TABLE_NAME > 'ports'`
    db.query(sql, (err, tables) => {
      if (err) reject(err);
      resolve(tables);
    })
  })
}

const { Transform } = require('stream');

const cleanFile = async (tname) => {
  // Use streams to read, transform, and write the file
  const inputFile = tname + '.sql';
  const outputFile = tname + '.clean.sql';

  const readStream = fs.createReadStream(inputFile, { encoding: 'utf8' });
  const writeStream = fs.createWriteStream(outputFile);
  
  return new Promise((resolve, reject) => {
    readStream.pipe(new Transform({
      transform(chunk, encoding, callback) {
        const data = chunk.toString()
          .replace(/'0000-00-00'/mg, 'NULL')
          .replace(/^--[^\n]+$/mg, '')
          .replace(/^\/\*.*?\*\/;$/mg, '')
          .replace(/^CHANGE MASTER TO MASTER_LOG_FILE=.*$/mg, '');
        callback(null, data);
      }
    }))
      .pipe(writeStream)
      .on('finish', () => {
        // console.log(`File processed and written to ${outputFile}`);
        try {
          // fs.renameSync(outputFile, inputFile); // Rename cleaned file back to original
          // readStream.destroy();
          // writeStream.destroy();
          resolve();
        }catch(ex) {
          reject(ex)
        }
      })
      .on('error', (err) => {
        console.error('Error processing file:', err);
        reject('Error processing file:'+ err)
      })
  })
}

let onprogress = 0

const insertDB = async (tname, i, errtables) => {
  return new Promise((resolve, reject) => {
    onprogress++
    //await cleanFile(`${tname}_${i}`).catch(ex => {
    //  errtables.push(`${tname}_${i}`)
    //})

    const stat = fs.statSync(`${tname}_${i}.clean.sql`)

    console.log(`Migrating table ${tname}_${i} - ${(stat.size / 1024 / 1024).toFixed(2)}MB...`);
    const out = shell.exec(
      // `"C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysql.exe" -u root -h ************* -pTheOFE2024! the_collective_db < ${tname}.clean.sql`,
      `mysql -u root -h ************* -pTheOFE2024! the_collective_db < ${tname}_${i}.clean.sql`,
      { async:true }, () => {
        onprogress--
        if(out.stderr.match(/ERROR/)) {
          console.log('WIll retry ', `${tname}_${i}`)
          errtables.push(`${tname}_${i}`)
        } else {
          if(fs.existsSync(`${tname}_${i}.clean.sql`)) fs.unlinkSync(`${tname}_${i}.clean.sql`)
        }
        if(fs.existsSync(`${tname}_${i}.sql`)) fs.unlinkSync(`${tname}_${i}.sql`)
        resolve()
      }
    )
  })
}

// sleep delay function
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const migrate = async (tables) => {
  let errtables = []
  
  for (let tname of tables) {
    try {
      // const tname = table[`Tables_in_${cfgSource.database}`]
      // if (tname <= 'contact_role') continue
      console.log(`Reading table ${tname}...`);
      let i = 0
      // for(let i = 0;i<280;i++) {
      //   console.log(i)
      //   while (onprogress > 5) {
      //     console.log(`Too many connections (${onprogress}), waiting 10s`)
      //     await sleep(10000)
      //   }
      shell.exec(
        // `"C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysqldump.exe" --hex-blob --master-data --set-gtid-purged=OFF -u dev_team -h *************** -px2e7fT8rNWJsChWu the_collective_db ${tname} > ${tname}.sql`,
        // `mysqldump --single-transaction --quick --insert-ignore --extended-insert --hex-blob --master-data --set-gtid-purged=OFF -u dev_team -h *************** -px2e7fT8rNWJsChWu the_collective_db ${tname} --no-create-info --where "creation_date > DATE_ADD('2019-11-08', INTERVAL ${i*1} DAY) and creation_date <= DATE_ADD('2019-11-08', INTERVAL ${(i+1)*1} DAY)" > ${tname}_${i}.sql`,
        `mysqldump --single-transaction --quick --insert-ignore --extended-insert --hex-blob --master-data --set-gtid-purged=OFF -u dev_team -h *************** -px2e7fT8rNWJsChWu the_collective_db ${tname} --no-create-info > ${tname}_${i}.sql`,
        { silent: false }
      )
      //   const txt = fs.readFileSync(tname + '.sql', 'utf8')
      //   fs.writeFileSync(tname + '.sql', txt.replace(/^--[^\n]+$/mg,'').replace(/^\/\*.*?\*\/;$/mg,'').replace(/^CHANGE MASTER TO MASTER_LOG_FILE=.*$/mg,''))
      
      insertDB(tname, i, errtables).catch(ex => {
        console.log(ex)
      })
        
      //}
    } catch (err) { console.log(err)
      errtables.push(tname)
    }
  }
  
  return errtables
}

const main = async ()  =>{
  const tables = await getTables(dbTarget);
  let errtables = await migrate(tables.map(t => t[`table_name`]))
  // let errtables = await migrate(['vessel_position'])
  // if (errtables.length) {
  //   console.log('Retry 1:')
  //   errtables = await migrate(errtables)
  // }
  // if (errtables.length) {
  //   console.log('Retry 2:')
  //   errtables = await migrate(errtables)
  // }
  // if (errtables.length) {
  //   console.log('Retry 3:')
  //   errtables = await migrate(errtables)
  // }
  if (errtables.length) fs.writeFileSync('errtables.txt', errtables.join('\n'))
  process.exit(0);
}

main()
