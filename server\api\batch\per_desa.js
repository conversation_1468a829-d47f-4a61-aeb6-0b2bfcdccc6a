require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');

// Default source file
const SRC_FILE = process.argv[2] || 'p1234.csv';
console.log(`Processing file: ${SRC_FILE}`);

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  port: parseInt(process.env.DB_PORT || '3307'),
  acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

// Function to get a connection with retry logic
const getConnection = () => {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error('Error getting MySQL connection:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Retrying connection in 2 seconds...');
          setTimeout(() => {
            getConnection().then(resolve).catch(reject);
          }, 2000);
        } else {
          reject(err);
        }
      } else {
        // Set session variables to increase timeouts
        connection.query('SET SESSION wait_timeout=300', (err) => {
          if (err) console.error('Error setting wait_timeout:', err);

          connection.query('SET SESSION interactive_timeout=300', (err) => {
            if (err) console.error('Error setting interactive_timeout:', err);
            resolve(connection);
          });
        });
      }
    });
  });
};

// Function to execute a query with retry logic
const executeQuery = async (sql, params, retries = 3) => {
  let connection;
  try {
    connection = await getConnection();
    const query = util.promisify(connection.query).bind(connection);
    return await query(sql, params);
  } catch (error) {
    if (retries > 0 && (error.code === 'PROTOCOL_CONNECTION_LOST' ||
                         error.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR' ||
                         error.message.includes('timeout'))) {
      console.log(`Query failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return executeQuery(sql, params, retries - 1);
    }
    throw error;
  } finally {
    if (connection) connection.release();
  }
};

// Process records in batches
async function processBatch(records, startIndex, batchSize) {
  const endIndex = Math.min(startIndex + batchSize, records.length);
  const batch = records.slice(startIndex, endIndex);
  const results = [];

  console.log(`Processing batch ${startIndex + 1} to ${endIndex} of ${records.length}`);

  for (const record of batch) {
    try {
      // Execute SQL query with retry logic
      // const result = await executeQuery(
      //   'SELECT COUNT(1) as Sisa FROM prm_pbdt pp WHERE TipeData BETWEEN 100 AND 199 AND KepemilikanRumah = 1 AND pp.Kabupaten = ? AND Kecamatan = ? AND Kelurahan = ? AND Sumber IS NOT NULL',
      //   [record['KABUPATEN/KOTA'], record.KECAMATAN, record.DESA]
      // );

      // Update SISA value
      // results.push({
      //   ...record,
      //   SISA: result[0].Sisa
      // });

      const result = await executeQuery(
        'SELECT * FROM tmp_rekap_bdt WHERE Kabupaten = ? AND Kecamatan = ? AND Kelurahan = ?',
        [record['KABUPATEN/KOTA'], record.KECAMATAN, record.DESA]
      );

      // Update SISA value
      // console.log(result.find(r => r.TahunBDT == 2015))
      results.push({
        ...record,
        // SISA: result[0].Sisa
        2015: result.find(r => r.TahunBDT == 2015)?.Jml || 0,
        2018: result.find(r => r.TahunBDT == 2018)?.Jml || 0,
        2019: result.find(r => r.TahunBDT == 2019)?.Jml || 0,
        2020: result.find(r => r.TahunBDT == 2020)?.Jml || 0,
        2021: result.find(r => r.TahunBDT == 2021)?.Jml || 0,
        2022: result.find(r => r.TahunBDT == 2022)?.Jml || 0,
        2023: result.find(r => r.TahunBDT == 2023)?.Jml || 0,
        2024: result.find(r => r.TahunBDT == 2024)?.Jml || 0,
        2025: result.find(r => r.TahunBDT == 2025)?.Jml || 0,
      });

      // Log progress occasionally
      if ((startIndex + results.length) % 10 === 0 || results.length === batch.length) {
        console.log(`Processed ${startIndex + results.length} of ${records.length} records`);
      }
    } catch (error) {
      console.error(`Error processing record for ${record['KABUPATEN/KOTA']}, ${record.KECAMATAN}, ${record.DESA}:`, error);
      // Keep the original record if there's an error
      results.push(record);
    }
  }

  return results;
}

async function updateSisaPerDesa() {
  try {
    console.log(`Reading CSV file: ${SRC_FILE}`);
    // Read CSV file
    const fileContent = fs.readFileSync(SRC_FILE, 'utf-8');
    const records = csv.parse(fileContent, {
      columns: true,
      skip_empty_lines: true
    });

    console.log(`Found ${records.length} records in CSV file`);

    // Check if SISA column exists
    const hasSisaColumn = records.length > 0 && 'SISA' in records[0];
    console.log(`SISA column ${hasSisaColumn ? 'exists' : 'does not exist'} in the input file`);

    // Process in batches of 50 records
    const BATCH_SIZE = 50;
    let allProcessedRecords = [];

    for (let i = 0; i < records.length; i += BATCH_SIZE) {
      const processedBatch = await processBatch(records, i, BATCH_SIZE);
      allProcessedRecords = [...allProcessedRecords, ...processedBatch];

      // Save intermediate results every 200 records
      if (allProcessedRecords.length % 200 === 0 || allProcessedRecords.length === records.length) {
        const tempOutput = stringify(allProcessedRecords, {header: true});
        const backupFile = `${SRC_FILE}.backup`;

        // Create a backup of the original file if it doesn't exist
        if (!fs.existsSync(backupFile) && allProcessedRecords.length === records.length) {
          fs.copyFileSync(SRC_FILE, backupFile);
          console.log(`Created backup of original file: ${backupFile}`);
        }

        // Write intermediate results
        fs.writeFileSync(SRC_FILE, tempOutput);
        console.log(`Saved progress: ${allProcessedRecords.length} of ${records.length} records`);
      }
    }

    console.log('CSV file has been updated successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted, closing connections...');
  pool.end(err => {
    if (err) console.error('Error closing MySQL pool:', err);
    process.exit(2);
  });
});

updateSisaPerDesa();