<template>
  <Page title="Validasi Data Kebencanaan" :sidebar="true">
    <Sidebar :value.sync="area" />
    <div
      style="padding: 10px; width: calc(100vw - 330px)"
      v-show="area.Kelurahan"
    >
      <DesaGlance
        :dbparams="area"
        @clickTambahBaru="ClickTambahBaru"
        :isAddNew="true"
      />
      <Grid
        :datagrid.sync="datagrid"
        dbref="BCN.ValidasiData"
        :dbparams="area"
        :disabled="true"
        height="calc(100vh - 250px)"
        :columns="[
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain',
            filter: {
              type: 'search',
            },
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: '<PERSON>a',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
            filter: {
              type: 'search',
            },
          },
          {
            name: '<PERSON><PERSON><PERSON>',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'IDBDT',
            value: 'IDBDT',
            class: 'center',
          },
          {
            name: '<PERSON><PERSON>',
            value: 'StatusTanah',
          },
          {
            name: 'Luas',
            value: 'LuasTanah',
          },
          {
            name: 'Skor',
            value: 'SkorTag',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
        ]"
      >
        <template v-slot:row-NIK="{ row }">
          <v-btn text small color="primary" @click="OpenDetail(row.NoRef)">
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
            >mdi-account-check</v-icon
          >
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-IDBDT="{ row }">
          <v-icon
            v-tooltip="row.IDBDT"
            v-show="row.IDBDT"
            @click="CopyText(row.IDBDT)"
          >
            mdi-content-copy
          </v-icon>
        </template>
        <template v-slot:row-NamaData="{ row }">
          <div v-if="row.Intervensi" style="color: green">
            {{ row.Intervensi }}
          </div>
          <div v-else>
            {{ row.NamaData }}
          </div>
        </template>
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from './ValidasiDetail.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
  },
  data: () => ({
    datagrid: [],
    area: {},
    showDetailModal: false,
    selectedRef: null,
  }),
  methods: {
    CopyText(txt) {
      navigator.clipboard.writeText(txt)
      this.$api.notify('IDBDT sudah tersalin')
    },
    ClickTambahBaru() {
      this.selectedRef = null
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
  },
}
</script>
