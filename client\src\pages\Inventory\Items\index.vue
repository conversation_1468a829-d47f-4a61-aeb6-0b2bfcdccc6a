<template>
  <div style="padding:10px;">
    <XSelect :items="itemgroup" :value.sync="GroupID" />

    <Grid
      :datagrid.sync="datatable"
      :columns="columns"
      dbref="INV.ItemList"
      :dbparams="tableparams"
      height="400px"
    >
    </Grid>
  </div>
</template>

<script>
//import Vue from "vue";
import { Select } from "@/components/Forms";
import Grid from "@/components/Grid";
import "material-design-icons-iconfont/dist/material-design-icons.css";

export default {
  components: {
    // Input,
    Select,
    Grid,
  },
  data: () => ({
    text: "asd",
    files: [],
    datatable: [],
    itemgroup: [],
    GroupID: null,
    columns: [
      {
        name: "Column 1",
      },
      {
        name: "Column 2",
      },
    ],
  }),
  created() {
    this.getItemGroup();
  },
  computed: {
    tableparams() {
      return {
        GroupID: 2,
      };
    },
  },
  methods: {
    async getItemGroup() {
      this.itemgroup = (await this.$api.select("itemgroup")).toDropdown();
      this.GroupID = this.itemgroup[0].value;
    },
    // async handleDataUpdate(idx, data) {
    //   let ret = await this.$api.call("INV_SavItemGroup", data);
    //   if (!ret.success) this.getItemGroup();
    // },
    // async handleDataDelete(idx) {
    //   let ret = await this.$api.call("INV_DelItemGroup", this.datatable[idx]);
    //   if (ret.success) this.datatable.splice(idx, 1);
    // }
  },
};
</script>
