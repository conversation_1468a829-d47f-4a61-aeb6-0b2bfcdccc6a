<template>
  <Modal title="PROPOSAL" :show.sync="xshow" width="900px" @onSubmit="Save">
    <div style="display: flex; font-size: 14px">
      <div>
        <div style="padding: 10px; background: #f3f3f3">
          <i class="fa fa-globe"></i>&nbsp;&nbsp;PETA LOKASI
        </div>
        <div id="MapDiv" style="position: relative">
          <Map
            :lat.sync="forms.GeoLat"
            :lon.sync="forms.GeoLng"
            style="width: 450px; height: 340px; display: block"
          />
        </div>
      </div>
      <div>
        <div
          style="
            padding: 10px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
          "
        >
          <div class="iblock" style="flex: 1; padding-left: 10px">
            <i class="fa fa-file"></i>&nbsp;&nbsp;KTP
          </div>
          <div class="iblock" style="flex: 1; padding-left: 10px">
            <i class="fa fa-file"></i>&nbsp;&nbsp;Proposal
          </div>
        </div>
        <div style="display: flex">
          <Uploader :value.sync="forms.KTP" accept=".jpg"></Uploader>
          <Uploader
            :value.sync="forms.PermohonanBansos"
            accept=".pdf"
          ></Uploader>
        </div>
        <div
          style="
            padding: 10px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
          "
        >
          <div class="iblock" style="width: 140px; padding-left: 10px">
            <i class="fa fa-user"></i>&nbsp;&nbsp;SERTIFIKAT
          </div>
          <div class="iblock" style="flex: 1; padding-left: 10px">
            <i class="fa fa-home"></i>&nbsp;&nbsp;FOTO LAHAN
          </div>
        </div>
        <div style="display: flex">
          <Uploader
            :value.sync="forms.SertifikatTanah"
            accept=".jpg,.jpeg,.jfif,.heic,.png,.pdf"
          ></Uploader>
          <Uploader :value.sync="forms.Rumah0" accept=".jpg"></Uploader>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  components: {},
  data: () => ({
    xshow: false,
    forms: {},
    showRAB: false,
    rab: {},
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
  },
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
    rabFilters() {
      let f = ['A', 'B', 'C', 'D', 'E']
      // console.log(f)
      return f
    },
  },
  methods: {
    async populate() {
      this.loading = true
      if (this.nik) {
        let { data } = await this.$api.call('RLK.SelPersonProposal', {
          NIK: this.nik,
        })
        this.forms = data[0]
      } else {
        this.forms = {}
      }
      this.loading = false
    },
    async Save() {
      this.error = ''
      let ret = await this.$api.call('RLK.SavPersonProposal', {
        ...this.forms,
        GeoLoc: this.forms.GeoLat
          ? this.forms.GeoLat + '|' + this.forms.GeoLng
          : null,
      })
      if (ret.success) {
        this.xshow = false
        this.$emit('save')
      } else this.error = ret.message
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
