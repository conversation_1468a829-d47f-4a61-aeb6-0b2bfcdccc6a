<template>
  <div style="height: calc(100vh - 460px)">
    <BarChart
      :chart-data="datacollection"
      :options="chartOptions"
      class="yearly-chart"
    ></BarChart>
  </div>
</template>
<script>
import Bar<PERSON>hart from '../../components/Charts/Bar.vue'
export default {
  components: {
    BarChart,
  },
  data: () => ({
    progress: {},
    datacollection: {},
    chartOptions: {
      title: {
        display: true,
        text: 'Progress Validasi RTLH',
        fontSize: '16',
      },
      legend: {
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
        position: 'bottom',
      },
      responsive: true,
      maintainAspectRatio: false,
    },
  }),
  props: {
    tipeData: [Number, String],
  },
  watch: {
    tipeData() {
      this.populateProgress()
      this.populateChart()
    },
  },
  created() {
    this.populateProgress()
    this.populateChart()
  },
  methods: {
    async populateProgress() {
      //   let { data } = await this.$api.call('PRM.SelChartProgress', {
      //     TipeData: this.tipeData,
      //   })
      //   this.progress = data[0]
      //   this.progress.TotalIntervensi =
      //     0 +
      //     data[0].APBN +
      //     data[0].APBD1 +
      //     data[0].APBD2 +
      //     data[0].CSR +
      //     data[0].Lain +
      //     data[0].Validasi +
      //     data[0].DiluarPrioritas
      //   this.progress.SisaPBDT = data[0].TotalData - this.progress.TotalIntervensi
    },
    async populateChart() {
      let { data } = await this.$api.call('PRM_SelChartValidasiKab', {})
      let labels = [
        'BANJARNEGARA',
        'BANYUMAS',
        'BATANG',
        'BLORA',
        'BOYOLALI',
        'BREBES',
        'CILACAP',
        'DEMAK',
        'GROBOGAN',
        'JEPARA',
        'KARANGANYAR',
        'KEBUMEN',
        'KENDAL',
        'KLATEN',
        'KOTA MAGELANG',
        'KOTA PEKALONGAN',
        'KOTA SALATIGA',
        'KOTA SEMARANG',
        'KOTA SURAKARTA',
        'KOTA TEGAL',
        'KUDUS',
        'MAGELANG',
        'PATI',
        'PEKALONGAN',
        'PEMALANG',
        'PURBALINGGA',
        'PURWOREJO',
        'REMBANG',
        'SEMARANG',
        'SRAGEN',
        'SUKOHARJO',
        'TEGAL',
        'TEMANGGUNG',
        'WONOGIRI',
        'WONOSOBO',
      ]
      let colors = {
        Target: '#E91E63',
        Validasi: '#3F51B5',
      }
      let ds = []
      let total = {}
      data.forEach((d) => {
        let dx = {
          type: 'bar',
          data: [],
          baseColor: colors[d.Label],
          pointStyle: 'rectRot',
          pointRadius: 3,
        }
        total[d.Label] = 0
        Object.keys(d).forEach((k) => {
          if (k !== 'Label') {
            dx.data.push(d[k])
            total[d.Label] += d[k]
          }
        })
        dx.label = `${d.Label} (${total[d.Label].toLocaleString()})`
        ds.push(dx)
      })

      this.datacollection = {
        labels: labels,
        datasets: ds,
      }
    },
    randomScalingFactor() {
      return Math.floor(Math.random() * (50 - 5 + 1)) + 5
    },
  },
}
</script>
<style lang="scss">
.realisasi-chart {
  canvas {
    height: 300px;
  }
}
</style>
