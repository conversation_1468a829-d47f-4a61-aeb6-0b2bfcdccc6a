const https = require('https')
const fs = require('fs')
const path = require('path')
const WebSocket = require('ws')

// WEB SOCKET
module.exports = {
  websocket() {
    let wss = null
    if (
      fs.existsSync(
        '/etc/letsencrypt/live/simperum.disperakim.jatengprov.go.id/fullchain.pem'
      )
    ) {
      const server = https.createServer({
        cert: fs.readFileSync(
          '/etc/letsencrypt/live/simperum.disperakim.jatengprov.go.id/fullchain.pem'
        ),
        key: fs.readFileSync(
          '/etc/letsencrypt/live/simperum.disperakim.jatengprov.go.id/privkey.pem'
        ),
      })

      wss = new WebSocket.Server({server, port: 8289})
      server.listen(8289)
    } else {
      wss = new WebSocket.Server({port: 8289})
    }

    wss.on('connection', ws => {
      // console.log('client connected')
      ws.on('message', message => {
        console.log(`Received message => ${message}`)
      })
      // ws.send("Hello! Message From Server!!");
    })
    console.log('WebSocket running in port 8289')
  },
}
