<template>
  <Page title="<PERSON><PERSON><PERSON><PERSON>">
    <iframe
      :src="docUrl"
      style="width: 100%; height: 100%"
      frameborder="0"
    ></iframe>
    <div class="verification-container">
      <div class="verification-card">
        <div class="verification-header">
          <h2>Doku<PERSON>li</h2>
          <div class="verification-badge">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
        </div>

        <div class="verification-details">
          <div class="detail-row">
            <span class="detail-label">Verifikator:</span>
            <span class="detail-value">{{ form.Verifikator }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Tanggal:</span>
            <span class="detail-value">{{ form.TglVerifikasi | format }}</span>
          </div>
        </div>

        <button class="download-btn" @click="downloadPDF">
          Download Dokumen
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
        </button>
      </div>
    </div>
  </Page>
</template>

<script>
import Page from '@/components/Page.vue'

export default {
  components: {
    Page,
  },
  data() {
    return {
      form: {},
    }
  },
  mounted() {
    this.populate()
  },
  computed: {
    docUrl() {
      return (
        this.$api.url +
        `/uploads/docs/${this.$route.params.id.replace(/-/, '/')}.pdf`
      )
    },
  },
  methods: {
    async populate() {
      let ret = await this.$api.call('EVO.SelDocument', {
        KeyUrl: this.$route.params.id,
      })
      this.form = ret.data[0]
    },
    downloadPDF() {
      // TODO: Implement actual PDF download logic
      // console.log('Downloading verified document...')
      window.open(this.docUrl, '_blank')
      // This would typically call an API endpoint to get the PDF
    },
  },
}
</script>

<style scoped>
.verification-container {
  display: flex;
  /* justify-content: center; */
  align-items: center;
  /* min-height: calc(100vh - 120px); */
  padding: 2rem;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 450px;
  max-width: 100vw;
}

.verification-card {
  width: 100%;
  max-width: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 2rem;
  text-align: center;
}

.verification-header {
  margin-bottom: 2rem;
  position: relative;
}

.verification-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.verification-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #4caf50;
  color: white;
  margin: 0 auto;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: #666;
}

.detail-value {
  font-weight: 600;
  color: #333;
}

.download-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
  padding: 0.75rem 1.5rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.download-btn:hover {
  background: #1d4ed8;
}

.download-btn svg {
  margin-left: 0.5rem;
}
</style>
