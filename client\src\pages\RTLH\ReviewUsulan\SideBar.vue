<template>
  <div class="sidebar-validasi">
    <div style="display: flex; font-size: 12px" v-if="tabs.length">
      <div
        class="tab"
        v-show="tabs.includes(2)"
        @click="tabId = 2"
        :class="{
          active: tabId == 2,
        }"
      >
        BANKEU
      </div>
      <div
        class="tab"
        v-show="tabs.includes(12)"
        @click="tabId = 12"
        :class="{
          active: tabId == 12,
        }"
      >
        BANKAB
      </div>
      <div
        class="tab"
        v-show="tabs.includes(1)"
        @click="tabId = 1"
        :class="{
          active: tabId == 1,
        }"
      >
        BSPS
      </div>
      <!-- <div
        class="tab"
        v-show="tabs.includes(9)"
        @click="tabId = 9"
        :class="{
          active: tabId == 9,
        }"
      >
        BSPS-KL
      </div> -->
      <div
        class="tab"
        v-show="tabs.includes(4)"
        @click="tabId = 4"
        :class="{
          active: tabId == 4,
        }"
      >
        CSR
      </div>
      <div
        class="tab"
        v-show="tabs.includes(13)"
        @click="tabId = 13"
        :class="{
          active: tabId == 13,
        }"
      >
        BAZNAS
      </div>
    </div>
    <div style="padding: 10px; display: flex">
      <XSelect
        v-show="!searchMode"
        dbref="PRM.SelProposal"
        :value.sync="proposal"
        :valueAsObject="true"
        width="80px"
        style="margin-right: 10px"
        @change="UpdateValue"
      />
      <XSelect
        v-show="!searchMode"
        dbref="PRM.SelPBDTCity"
        :dbparams="{}"
        :value.sync="kabupaten"
        :valueAsObject="true"
        width="190px"
        @change="UpdateValue"
      />
      <XInput
        type="text"
        v-show="searchMode"
        :value.sync="keyword"
        placeholder="Cari .."
        width="280px"
      />
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="!searchMode"
        @click="searchMode = !searchMode"
        >search</v-icon
      >
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="searchMode"
        @click="searchMode = !searchMode"
        >clear</v-icon
      >
    </div>
    <div>
      <div style="font-size: 14px; text-align: center" v-show="loading">
        <v-icon left>mdi-loading mdi-spin</v-icon>
        Loading ...
      </div>
      <List
        :items.sync="itemarea"
        :filters="{
          keyword: keyword,
          filter: filterArea,
        }"
        :height="`calc(100vh - ${!tabs.length ? 160 : 200}px)`"
        @itemClick="AreaClicked"
        :selectOnLoad="true"
        v-show="!loading && itemarea.length"
      >
        <template v-slot="{ row }">
          <div :class="`ordr-${row.Ordr}`">
            <div
              :style="{
                color: row.KelTipe == 'KEL' ? 'rgb(30, 136, 229)' : '#333',
              }"
            >
              <v-icon
                v-show="row.Ordr == 2"
                style="height: 16px"
                :style="{ color: warnaDesa[row.Priority] || 'palegreen' }"
              >
                mdi-circle-medium
              </v-icon>
              {{ row.KelTipe == 'KEL' ? 'K.' : '' }}
              {{ row.AreaName }}

              <span
                v-if="row.DataTag"
                style="
                  font-size: 10px;
                  background: lightblue;
                  padding: 3px;
                  border-radius: 3px;
                "
              >
                {{ row.DataTag }}
              </span>
            </div>
            <div class="status">
              <!-- <div
                :class="
                  `badge s-${row.Rekom ? row.Rekom.replace(' ', '') : ''}`
                "
              >
                {{ row.Rekom }}
              </div> -->
              <div
                v-for="(r, idx) in (row.Rekom || '').split(',')"
                :key="idx"
                v-tooltip="'REKOM ' + r"
                v-show="row.Rekom"
                class="s-REKOM"
                :class="'s-REKOM' + r"
              >
                {{ r }}
              </div>
              <div
                class="badge s-jml"
                v-tooltip="'Jml Disetujui/Jml Usulan'"
                v-show="row.JmlBansos !== null"
              >
                {{ row.Approved }} / {{ row.JmlBansos }}
              </div>
            </div>
          </div>
        </template>
      </List>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data: () => ({
    kabupaten: { val: null, txt: null },
    proposal: { InputName: new Date().getFullYear() },
    kodeDagri: '',
    selectedArea: {},
    searchMode: false,
    keyword: '',
    itemarea: [],
    tabId: 2,
    loading: false,
    pending: false,
    warnaDesa: ['palegreen', 'red', 'yellow', 'palegreen'],
  }),
  props: {
    value: Object,
    dbref: {
      type: String,
      default: 'PRM.SelProposalArea',
    },
    tahun: {
      type: [String, Number],
      default: () => {
        return new Date().getFullYear()
      },
    },
    tabs: {
      type: Array,
      default: () => [2, 12, 1, 9, 4, 13],
    },
    rebind: Number,
  },
  watch: {
    value(val) {
      if (val.Tahun) this.proposal = { InputName: val.Tahun }
      if (val.KodeDagri && val.KodeDagri != this.kodeDagri)
        this.getByKodeDagri(val.KodeDagri)
      else {
        if (val.Kabupaten) this.kabupaten = { txt: val.Kabupaten }
      }
    },
    searchMode(val) {
      if (!val) this.keyword = ''
    },
    listParams(val) {
      if (!this.pending) {
        this.oldParams = JSON.stringify(val)
        this.$nextTick(() => {
          this.Populate()
        })
      }
    },
    tabId() {
      this.UpdateValue()
    },
  },
  created() {
    this.proposal = {
      InputName: this.tahun,
    }
    this.tabId = this.tabs[0]
  },
  mounted() {
    if (Object.keys(this.$route.query).length) {
      this.area = this.$route.query
    } else if (window.sessionStorage.getItem('side-area')) {
      setTimeout(() => {
        let areaVal = JSON.parse(window.sessionStorage.getItem('side-area'))
        this.$emit('update:value', {
          ...areaVal,
          Tahun: this.proposal.InputName,
          ProposalID: this.proposal.ProposalID,
        })
      }, 500)
    }
  },
  computed: {
    listParams() {
      return {
        Kabupaten: this.kabupaten.txt,
        SumberID: this.tabId,
        ProposalID: this.proposal.ProposalID,
      }
    },
  },
  methods: {
    ...mapActions(['setPageFocused']),
    async Populate() {
      if (!this.listParams.Kabupaten) return

      this.loading = true
      let res = await this.$api.call(this.dbref, this.listParams, {
        useCache: true,
      })
      if (res.data) {
        let jmlBansos = 0
        let jmlApproved = 0
        for (let i = res.data.length - 1; i >= 0; i--) {
          let d = res.data[i]
          if (d.Ordr == 1) {
            d.JmlBansos = jmlBansos
            d.Approved = jmlApproved
            jmlBansos = 0
            jmlApproved = 0
          } else {
            jmlBansos += d.JmlBansos
            jmlApproved += d.Approved
          }
        }
        this.itemarea = res.data
      }
      this.loading = false
    },
    AreaClicked(item) {
      if (!item || !item.AreaID) return
      this.pending = true
      setTimeout(() => {
        this.pending = false
      }, 500)

      this.setPageFocused(true)
      let areaVal = {
        tabId: this.tabId,
        Sumber: this.tabId,
        Tahun: this.proposal.InputName,
        ProposalID: this.proposal.ProposalID,
        Kabupaten: this.kabupaten.txt,
        Kecamatan: item.Kecamatan,
        Kelurahan: item.Ordr == 1 ? null : item.AreaName,
        KelurahanID: item.Ordr == 1 ? null : item.AreaID,
        KodeDagri: item.Ordr == 1 ? null : item.KodeDagri,
      }
      window.sessionStorage.setItem('side-area', JSON.stringify(areaVal))
      this.$emit('update:value', areaVal)
    },
    filterArea(item) {
      return (
        item.AreaName.match(new RegExp(this.keyword, 'i')) && item.JmlBansos
      )
    },
    async getByKodeDagri(kodeDagri) {
      if (this.kodeDagri == kodeDagri) return
      this.kodeDagri = kodeDagri
      let res = await this.$api.call('Arch.SelFullArea', {
        KodeDagri: kodeDagri,
      })
      if (res.success) {
        let item = res.data[0]
        let areaVal = {
          ...item,
          tabId: this.tabId,
          Sumber: this.tabId,
          Tahun: this.proposal.InputName,
          ProposalID: this.proposal.ProposalID,
          KodeDagri: kodeDagri,
        }
        window.sessionStorage.setItem('side-area', JSON.stringify(areaVal))
        this.$emit('update:value', areaVal)
      }
    },
    UpdateValue() {
      this.$emit('update:value', {
        tabId: this.tabId,
        Sumber: this.tabId,
        Tahun: this.proposal.InputName,
        ProposalID: this.proposal.ProposalID,
        Kabupaten: this.kabupaten.txt,
      })
    },
  },
}
</script>
<style lang="scss">
.sidebar-validasi {
  background: white;
  .tab {
    text-align: center;
    flex: 1;
    font-size: 13px;
    padding: 5px 10px;
    background: #ddd;
    color: gray;
    border-right: 0.5px solid silver;
    cursor: pointer;

    &.active {
      background: white;
      font-weight: bold;
      border: 1px solid silver;
      border-bottom: transparent;
    }
  }
}
</style>
