<template>
  <v-alert
    border="left"
    type="warning"
    v-if="isMonevNotComplete"
    style="margin-bottom: 10px"
  >
    <v-row align="center" style="padding: 5px 0 0 15px">
      Usulan &nbsp;<strong>TIDAK AKAN DIPROSES</strong>&nbsp; lebih lanjut ji<PERSON> & LPJ tahun sebelumnya belum lengkap
    </v-row>
    <v-row style="justify-content: end; padding-bottom: 5px">
      <v-btn
        text
        outlined
        @click="$router.push('/Main/RTLH/Monev/')"
        color="black"
      >
        BUKA MONEV
      </v-btn>
      <v-btn @click="isMonevNotComplete = false" style="margin-right: 15px">
        OK
      </v-btn>
    </v-row>
  </v-alert>
</template>
<script>
export default {
  data: () => ({
    isMonevNotComplete: false,
  }),
  props: {
    area: Object,
  },
  watch: {
    'area.<PERSON><PERSON><PERSON>an'() {
      this.CekMonev()
    },
  },
  methods: {
    async CekMonev() {
      let d = await this.$api.call('PRM_SelMonevComplete', this.area)
      if (d.data.length > 0) this.isMonevNotComplete = true
      else this.isMonevNotComplete = false
    },
  },
}
</script>
