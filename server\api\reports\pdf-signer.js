const { PDFDocument, rgb, StandardFonts } = require('pdf-lib');
const forge = require('node-forge');
const fs = require('fs');
const QRCode = require('qrcode')
const moment = require('moment')

async function signPDF(inputPath) {
  // Load the PDF
  const pdfBytes = fs.readFileSync(inputPath);
  const pdfDoc = await PDFDocument.load(pdfBytes);

  // Add signature field
  // const pages = pdfDoc.getPages();
  // const firstPage = pages[0];
  // const { width, height } = firstPage.getSize();

  let addf = moment().format('DDHHmmssSSS') 
  let year = moment().format('YYYY')
  const linkUrl = `https://simperum.disperakim.jatengprov.go.id/doc-view/${year}-${addf}`
  let qrimg = new Promise((resolve, reject) => {
    QRCode.toFile(`./tmp/qrcode_${addf}.png`, 
      linkUrl, 
      (err) => {
        if(err) reject(err)
        resolve()
      })  
  })

  // Get the first page of the document
  const pages = pdfDoc.getPages()
  const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica)
  //qrcode
  await qrimg
  const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_${addf}.png`))
      
  let i = 1
  for(let firstPage of pages){    

    firstPage.drawRectangle({
      x: 87,
      y: 42,
      width: 120,
      height: 10,
      color: rgb(1, 1, 1),
    });    

    firstPage.drawRectangle({
      x: 87,
      y: 31,
      width: 175,
      height: 12,
      color: rgb(1, 1, 1),
    });

    firstPage.drawRectangle({
      x: 87,
      y: 20,
      width: 300,
      height: 12,
      color: rgb(1, 1, 1),
    });
    // Draw a string of text diagonally across the first page
    firstPage.drawText('Scan barcode disamping ', {
      x: 90,
      y: 44,
      size: 10,
      font: helveticaFont,
    })

    firstPage.drawText('untuk mengetahui keaslian kwitansi ini ', {
      x: 90,
      y: 33,
      size: 10,
      font: helveticaFont,
    })

    firstPage.drawText(linkUrl, {
      x: 90,
      y: 22,
      size: 10,
      font: helveticaFont,
    })

    firstPage.drawRectangle({
      x: 27,
      y: 12,
      width: 56,
      height: 56,
      color: rgb(1, 1, 1),
    });
    firstPage.drawImage(pngImage, {
      x: 30,
      y: 15,
      width: 50,
      height: 50,
    })

    i++
  }

  const result = await pdfDoc.save()
  let fn = `uploads/docs/${year}/${addf}.pdf`
  fs.writeFileSync(fn, result)
  return `uploads/docs/${year}/${addf}.pdf`
    
  // const pngImage = await pdfDoc.embedPng(fs.readFileSync(`./tmp/qrcode_kw${id}.png`))
  // firstPage.drawImage(pngImage, {
  //   x: 30,
  //   y: 15,
  //   width: 50,
  //   height: 50,
  // })
  // // Add signature annotation instead of form field
  // firstPage.drawRectangle({
  //   x: 50,
  //   y: 50,
  //   width: 200,
  //   height: 50,
  //   borderWidth: 1,
  //   borderColor: rgb(0, 0, 0),
  //   backgroundColor: rgb(1, 1, 1),
  // });

  // // Load certificate and private key
  // const certPem = fs.readFileSync('tmp/templates/certificate.pem', 'utf8');
  // const keyPem = fs.readFileSync('tmp/templates/privateKey.pem', 'utf8');
  
  // // Convert key if necessary
  // let privateKey;
  // if (keyPem.includes('BEGIN ENCRYPTED PRIVATE KEY')) {
  //   const pkey = forge.pki.decryptRsaPrivateKey(keyPem, 'simperum');
  //   if (!pkey) throw new Error('Failed to decrypt private key');
  //   privateKey = pkey;
  // } else if (keyPem.includes('BEGIN PRIVATE KEY') || keyPem.includes('BEGIN RSA PRIVATE KEY')) {
  //   privateKey = forge.pki.privateKeyFromPem(keyPem);
  // } else {
  //   throw new Error('Invalid private key format');
  // }

  // const cert = forge.pki.certificateFromPem(certPem);

  // // Create signature
  // const pdfBytesWithPlaceholder = await pdfDoc.save({ useObjectStreams: false });
  // const md = forge.md.sha256.create();
  // md.update(pdfBytesWithPlaceholder.toString('binary'), 'binary');
  // const signature = privateKey.sign(md);

  // // Embed the signature
  // const signatureHex = forge.util.bytesToHex(signature);
  // const signatureAnnotation = firstPage.drawText('Digitally Signed', {
  //   x: 55,
  //   y: 55,
  //   size: 12,
  //   color: rgb(0, 0, 0),
  // });

  // // Add signature metadata
  // pdfDoc.setTitle('Signed Document');
  // pdfDoc.setSubject('Digitally Signed PDF');
  // pdfDoc.setAuthor('Simperum');
  // pdfDoc.setKeywords(['digital', 'signature']);
  // pdfDoc.setModificationDate(new Date());

  // // Save the signed PDF
  // const signedPdfBytes = await pdfDoc.save();
  // fs.writeFileSync(inputPath.replace(/\.pdf$/, '-signed.pdf'), signedPdfBytes);
}

module.exports = { signPDF };