<template>
  <Modal title="LPJ" :show.sync="xshow" width="400px" @onSubmit="Save">
    <div style="display: flex" class="form-inline">
      <div class="iblock" style="width: 200px">
        <div style="padding: 5px">
          <Uploader
            label="LPJ"
            :value.sync="forms.CoverLPJ"
            accept=".pdf"
          ></Uploader>
          <!-- <CheckUpload
            label="Cover LPJ"
            accept=".pdf"
            :value.sync="forms.CoverLPJ"
          />
          <CheckUpload
            label="Surat Laporan Bansos"
            :value.sync="forms.LapBansos"
          />
          <CheckUpload
            label="Laporan TPK"
            accept=".pdf"
            :value.sync="forms.LapTPK"
          />
          <CheckUpload
            label="Surat Pernyataan Tanggung Jawab"
            accept=".pdf"
            :value.sync="forms.SuratTggJwb"
          />
          <CheckUpload
            label="Daftar Hadir"
            accept=".pdf"
            style="width: 300px"
            :value.sync="forms.DaftarHadir"
          />
          <CheckUpload
            label="<PERSON><PERSON><PERSON>"
            accept=".pdf"
            :value.sync="forms.BuktiUangKeluar"
          />
          <CheckUpload
            label="Bukti Setoran Pajak"
            accept=".pdf"
            :value.sync="forms.BuktiPajak"
          /> -->
          <!-- <CheckUpload
            label="FC Rekening Desa"
            accept=".pdf"
            :value.sync="forms.FCRekDesa"
          />
          <CheckUpload
            label="Berita Acara Pencairan"
            accept=".pdf"
            :value.sync="forms.BAPPencairan"
          /> -->
          <!-- <CheckUpload
            label="Realisasi Penggunaan Dana"
            accept=".pdf"
            :value.sync="forms.RealisasiDana"
          /> -->
          <!-- <CheckUpload
            label="Foto Kegiatan"
            accept=".pdf"
            :value.sync="forms.FotoKegiatan"
          /> -->
        </div>
      </div>
    </div>
    <!-- <template v-slot:left-action>
      <div style="padding-left: 10px; color: gray">
        <Checkbox text="Check Semua" :value.sync="checkAll" />
      </div>
    </template> -->
  </Modal>
</template>
<script>
// import CheckUpload from '../Pencairan/CheckUpload.vue'
export default {
  components: {
    // CheckUpload,
  },
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
    nik(val) {
      if (val) this.populate()
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x !== 'NIK') this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call('BLG.SelMonevCheck', {
        NIK: this.nik,
      })
      if (data && data.length) this.forms = data[0]
      else this.forms = {}
    },
    async Save() {
      let ret = await this.$api.call('BLG.SavMonevCheck', {
        ...this.forms,
        NIK: this.nik,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
