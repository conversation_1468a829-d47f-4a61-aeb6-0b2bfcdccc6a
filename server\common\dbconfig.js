const dbconfig = {
  connectionLimit: 20,         // Increased from 10 to handle more connections
  host: process.env.DB_HOST,
  port: '3307',
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  dateStrings: true,
  database: 'perum',
  acquireTimeout: 60000,       // 60 seconds to acquire a connection
  connectTimeout: 60000,       // 60 seconds to establish a connection
  timeout: 60000,              // 60 seconds for queries
  waitForConnections: true,    // Queue connection requests
  queueLimit: 0,               // Unlimited queue size
  // Add connection handling parameters to prevent memory leaks
  multipleStatements: false,   // Prevent multiple statements which can cause memory issues
  debug: false                 // Disable debug mode in production
}

module.exports = dbconfig