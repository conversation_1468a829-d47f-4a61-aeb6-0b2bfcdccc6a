/**
 * Memory and Resource Cleanup Script
 * 
 * This script performs periodic cleanup of resources to prevent memory leaks:
 * 1. Cleans up temporary files
 * 2. Monitors memory usage
 * 3. Forces garbage collection when needed
 * 
 * Run this script with: node --expose-gc cleanup.js
 */

const fs = require('fs');
const path = require('path');
const moment = require('moment');
const axios = require('axios');

// Configuration
const CLEANUP_INTERVAL = 60 * 60 * 1000; // Run cleanup every hour
const MEMORY_CHECK_INTERVAL = 5 * 60 * 1000; // Check memory every 5 minutes
const HIGH_MEMORY_THRESHOLD_MB = 1024; // 1GB
const SERVER_URL = 'http://localhost:8001'; // Local server URL

// Function to monitor memory usage
function monitorMemoryUsage() {
  const memoryUsage = process.memoryUsage();
  const memoryUsageMB = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024)
  };
  
  console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Memory Usage (MB):`, memoryUsageMB);
  
  // If memory usage is high, force garbage collection
  if (memoryUsageMB.heapUsed > HIGH_MEMORY_THRESHOLD_MB) {
    console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] High memory usage detected (${memoryUsageMB.heapUsed}MB), running garbage collection...`);
    
    if (global.gc) {
      global.gc();
      
      // Check memory after garbage collection
      const afterGC = process.memoryUsage();
      const afterGCMB = {
        rss: Math.round(afterGC.rss / 1024 / 1024),
        heapTotal: Math.round(afterGC.heapTotal / 1024 / 1024),
        heapUsed: Math.round(afterGC.heapUsed / 1024 / 1024)
      };
      
      console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] After GC - Memory Usage (MB):`, afterGCMB);
      console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Memory freed: ${memoryUsageMB.heapUsed - afterGCMB.heapUsed}MB`);
    } else {
      console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] To enable garbage collection, start with --expose-gc flag`);
    }
  }
}

// Function to clean up temporary files
async function cleanupTemporaryFiles() {
  console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Starting cleanup of temporary files...`);
  
  try {
    // Call the server's cleanup endpoint
    const response = await axios.get(`${SERVER_URL}/api/cleanres`);
    
    if (response.data.success) {
      console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Cleanup completed successfully`);
    } else {
      console.error(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Cleanup failed:`, response.data.message);
    }
  } catch (error) {
    console.error(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Error during cleanup:`, error.message);
  }
  
  // Also check for deadlocks
  try {
    const response = await axios.get(`${SERVER_URL}/api/check-deadlock`);
    if (response.data.data.hasDeadlock) {
      console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Deadlock detected and resolved`);
    }
  } catch (error) {
    console.error(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Error checking deadlocks:`, error.message);
  }
}

// Function to check database connections
async function checkDatabaseConnections() {
  console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Checking database connections...`);
  
  try {
    const response = await axios.get(`${SERVER_URL}/api/health`);
    console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Database health check:`, response.data.success ? 'OK' : 'Issues detected');
    
    if (!response.data.success) {
      console.error(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Database health issue:`, response.data.error);
    }
  } catch (error) {
    console.error(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Error checking database health:`, error.message);
  }
}

// Main function to run all cleanup tasks
async function runCleanupTasks() {
  console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Running cleanup tasks...`);
  
  try {
    // Monitor memory usage
    monitorMemoryUsage();
    
    // Clean up temporary files
    await cleanupTemporaryFiles();
    
    // Check database connections
    await checkDatabaseConnections();
    
    console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Cleanup tasks completed successfully`);
  } catch (error) {
    console.error(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Error during cleanup tasks:`, error.message);
  }
}

// Run cleanup tasks immediately on startup
runCleanupTasks();

// Schedule periodic cleanup
setInterval(runCleanupTasks, CLEANUP_INTERVAL);

// Schedule more frequent memory checks
setInterval(monitorMemoryUsage, MEMORY_CHECK_INTERVAL);

console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Cleanup service started`);
console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Memory check interval: ${MEMORY_CHECK_INTERVAL / 1000 / 60} minutes`);
console.log(`[${moment().format('YYYY-MM-DD HH:mm:ss')}] Cleanup interval: ${CLEANUP_INTERVAL / 1000 / 60 / 60} hours`);
