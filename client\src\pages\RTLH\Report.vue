<template>
  <Page title="Laporan" :sidebar="true">
    <div style="background: white; overflow-x: hidden; overflow-y: auto">
      <div class="rowgrp">DATABASE</div>
      <div class="rowlnk" v-if="coms['SEMUA DATA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptAllData' })"
        >
          Semua Data
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptRekapGender' })"
        >
          Rekap Gender
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptBNBAIntervention',
              groupsheet: 'Kabupaten',
            })
          "
        >
          BNBA Intervensi
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['BNBA SISA RTLH PER KABUPATEN']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptSisaPBDT' })"
        >
          BNBA Sisa RTLH per Kabupaten
        </v-btn>
      </div>
      <!-- <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptSisaPBDTMerahBNBA',
              groupsheet: 'Warna',
            })
          "
        >
          BNBA Sisa RTLH Warna per Kabupaten
        </v-btn>
      </div> -->
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptSisaDesaBinaan',
              groupsheet: 'Kabupaten',
            })
          "
        >
          BNBA Sisa RTLH Desa Binaan
        </v-btn>
      </div>
      <!-- <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptSisaPBDTWarna2' })"
        >
          BNBA Sisa RTLH Warna 2015
        </v-btn>
      </div> -->
      <div class="rowlnk" v-if="coms['REKAP SISA RTLH PER KABUPATEN']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptRekapPBDT' })"
        >
          Rekap Sisa + Intervensi (Kab/Kota)
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptRekapPBDTDesa',
              groupsheet: 'Kabupaten',
            })
          "
        >
          Rekap Sisa + Intervensi (Desa)
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptSisaPBDTKelurahan' })"
          >Rekap Sisa RTLH Prior (Desa)</v-btn
        >
      </div>
      <!-- <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptSisaPBDTWarna' })"
        >
          Rekap Sisa RTLH Warna (Desa)
        </v-btn>
      </div> -->
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptRekapValidasi',
              groupsheet: 'sheetname',
            })
          "
        >
          Rekap Validasi
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptFreePBDT' })"
          >Desa Bebas PBDT</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptDataPKE' })"
        >
          PROGRESS PKE
        </v-btn>
      </div>
      <div class="rowgrp">USULAN</div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptUsulanBankeu',
              groupsheet: 'Kabupaten',
            })
          "
        >
          Usulan Bankeu
        </v-btn>
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptRekapUsulan',
              groupsheet: 'Kabupaten',
            })
          "
          >Rekap Usulan Bankeu</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptUsulanBSPSAll' })"
          >Usulan BSPS</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptUsulanBSPSKLAll' })"
          >Usulan BSPS-KL</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptBerkasPenyaluran',
              action: '/reports/others/berkas-penyaluran',
            })
          "
          >Berkas Penyaluran</v-btn
        >
      </div>
      <div class="rowgrp">REKOM</div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptBerkasKurang',
            })
          "
          >Berkas Belum Lengkap</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'PRM_RptRekomendasi',
              custom: 'rtlh_rekom',
            })
          "
          >Rekomendasi Per Tahapan</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptRekomendasi' })"
          >Rekomendasi Semua</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptRekapDesdam' })"
          >Rekap Desa Dampingan</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptBNBADesdam' })"
          >BNBA Desa Dampingan</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptProgressTw' })"
          >Progress Triwulan</v-btn
        >
      </div>
      <div class="rowgrp">MONEV & LPJ</div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptMonev' })"
          >Progress Monev</v-btn
        >
      </div>
      <div class="rowlnk" v-if="coms['LAINNYA']">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'PRM_RptRekapLPJ' })"
          >Rekap LPJ</v-btn
        >
      </div>
    </div>
    <div>
      <ReportParams
        v-show="showParams"
        :options="reportOptions"
        :reportParams.sync="reportParams"
        :generatedUrl.sync="reportUrl"
        @generate="showReport = true"
      >
        <div v-if="reportOptions?.sp == 'PRM_RptSisaPBDT'">
          <br />
          <v-btn
            text
            outlined
            small
            color="primary"
            @click="DownloadCSV('bnba_sisa.csv')"
          >
            BNBA Sisa Semua Data
          </v-btn>
          <v-btn
            text
            outlined
            small
            color="primary"
            @click="DownloadCSV('bnba_sisa_bdt.csv')"
          >
            BNBA Sisa PBDT
          </v-btn>
          <v-btn
            text
            outlined
            small
            color="primary"
            @click="DownloadCSV('bnba_sisa_bdt15.csv')"
          >
            BNBA Sisa PBDT 2015
          </v-btn>
        </div>
        <div v-else-if="reportOptions?.sp == 'PRM_RptRekapPBDTDesa'">
          <br />
          <v-btn
            text
            outlined
            small
            color="primary"
            @click="DownloadCSV('rekap_sisa_kabupaten.csv')"
          >
            Rekap Sisa Kabupaten
          </v-btn>
          <v-btn
            text
            outlined
            small
            color="primary"
            @click="DownloadCSV('rekap_sisa_desa.csv')"
          >
            Rekap Sisa Desa
          </v-btn>
        </div>
      </ReportParams>
      <ReportViewer
        :url="reportUrl"
        :options="reportParams"
        :show.sync="showReport"
      />
    </div>
  </Page>
</template>
<script>
import ReportParams from '../../components/Report/Params.vue'
import ReportViewer from '../../components/ReportViewer.vue'
import { mapActions } from 'vuex'

export default {
  components: {
    ReportParams,
    ReportViewer,
  },
  data: () => ({
    coms: null,
    reportOptions: null,
    reportUrl: 'about:blank',
    showReport: false,
    showParams: false,
    reportParams: {},
  }),
  watch: {
    // reportParams(val) {
    //   if (val) this.showReport = true
    // },
  },
  created() {
    let coms = sessionStorage.getItem('coms-access')
    if (coms) this.coms = JSON.parse(coms)
  },
  methods: {
    ...mapActions(['setPageFocused']),
    Generate(evt, opts) {
      this.setPageFocused(true)
      this.showReport = false
      this.showParams = true
      opts.rptname = evt.target.innerText
      this.reportOptions = opts
    },
    DownloadCSV(file) {
      window.open(this.$api.url + '/reports/get/generated/' + file)
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
.rowgrp {
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  font-size: small;
  padding: 5px;
  background: #f3f3f3;
}
</style>
