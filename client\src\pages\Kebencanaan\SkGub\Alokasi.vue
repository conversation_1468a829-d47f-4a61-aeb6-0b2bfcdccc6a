<template>
  <Page title="Kebencanaan SK KADIN">
    <template v-slot:toolbar>
      <!-- <v-menu offset-y>
        <template v-slot:activator="{ on }">
          <v-btn small color="primary" v-on="on">DOWNLOAD</v-btn>
        </template>
        <v-list dense style="width: 280px">
          <v-list-item @click="DownloadSkGub(1)">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-word</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>SK Reguler</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="DownloadSkGub(1)">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-word</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>SK Perubahan</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="DownloadLampiran(2)">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-excel</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>LAMPIRAN SK Reguler</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="DownloadLampiran(2)">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-excel</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>LAMPIRAN SK Perubahan</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu> -->
    </template>
    <div class="padding">
      <div style="display: flex"></div>
      <Kabupaten />
    </div>
  </Page>
</template>
<script>
import Kabupaten from './Kabupaten.vue'

export default {
  components: {
    Kabupaten,
  },
  data: () => ({
    tahapan: [1, 2, 3, 4, 5, 6],
    proposal: {
      InputName: new Date().getFullYear(),
    },
    jenisKuota: 'kuota_kabupaten',
    hasAllocation: false,
  }),
  computed: {
    params() {
      let { InputName } = this.proposal
      return { Tahun: InputName }
    },
  },
  methods: {
    async Save() {
      await this.$api.call('BCN_SavAlokasi', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
    async DownloadSkGub(tahap) {
      let ret = await this.$api.post('/reports/template/SK_GUB.docx', {
        ...this.params,
        Tahap: tahap,
        transformResult: 1,
        sp: 'BCN_RptSkGub',
      })
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
    async DownloadLampiran(tahap) {
      let ret = await this.$api.post('/reports/template/SK_GUB_LAMPIRAN.ods', {
        ...this.params,
        Tahap: tahap,
        transformResult: 1,
        sp: 'BCN_RptSkGub',
      })
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
  },
}
</script>
<style lang="scss"></style>
