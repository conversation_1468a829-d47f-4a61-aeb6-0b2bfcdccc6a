<template>
  <v-app class="dense" :class="isMobile + ' ' + colorClass">
    <div class="update-notif" v-if="hasUpdate">
      Aplikasi telah diperbaharui,
      <v-btn dense small danger @click="updateApp()"> klik disini </v-btn>
      untuk memperbaharui.
    </div>
    <div class="installing-block" v-if="isInstalling">
      <div
        style="
          text-align: center;
          font-size: large;
          font-weight: bold;
          margin-top: 30vh;
        "
      >
        Please wait while we updating the application...
        <br />
        <br />
        <v-icon x-large>mdi-loading mdi-spin</v-icon>
      </div>
    </div>
    <NavBar v-if="menu && menu.length" />
    <!-- <v-main>
      <Login />
    </v-main> -->
    <router-view />
  </v-app>
</template>
<script>
import { mapGetters } from 'vuex'

// import { FadeTransition as PageTransition } from "vue2-transitions";
// import Header from "./pages/Header";
import NavBar from './components/NavBar.vue'
// import Login from "./components/pages/Login";

export default {
  name: 'App',
  metaInfo() {
    return {
      title: 'SIMPERUM',
      titleTemplate: '%s',
      meta: [
        {
          'http-equiv': 'Cache-Control',
          content: 'no-cache, no-store, must-revalidate',
        },
        {
          'http-equiv': 'Pragma',
          content: 'no-cache',
        },
        {
          'http-equiv': 'Expires',
          content: '0',
        },
      ],
    }
  },
  components: {
    NavBar,
    // Login
  },
  data: () => ({
    //
    hasUpdate: false,
    isInstalling: false,
  }),
  computed: {
    ...mapGetters({
      menu: 'getMenu',
      user: 'getUser',
      notification: 'getNotification',
    }),
    colorClass() {
      if (this.$route.fullPath.match(/Backlog/)) {
        return 'backlog'
      } else {
        return 'rtlh'
      }
    },
  },
  watch: {
    notification(val) {
      this.$toast.open({
        ...val,
        position: 'top',
        dismissible: true,
      })
    },
  },
  async created() {
    if (sessionStorage.getItem('hasUpdate')) {
      this.hasUpdate = sessionStorage.getItem('hasUpdate')
    }
    navigator.serviceWorker.register('/sw.js').then((reg) => {
      // reg.installing // the installing worker, or undefined
      // reg.waiting // the waiting worker, or undefined
      // reg.active // the active worker, or undefined

      reg.addEventListener('updatefound', () => {
        // A wild service worker has appeared in reg.installing!
        const newWorker = reg.installing

        newWorker.state
        console.log('updatefound', newWorker.state)
        if (newWorker.state == 'installing') {
          this.isInstalling = true
        }
        // "installing" - the install event has fired, but not yet complete
        // "installed"  - install complete
        // "activating" - the activate event has fired, but not yet complete
        // "activated"  - fully active
        // "redundant"  - discarded. Either failed install, or it's been
        //                replaced by a newer version

        // updatefound installing
        // statechange installed
        // statechange activating
        // statechange activated
        // Offline Ready

        newWorker.addEventListener('statechange', () => {
          console.log('statechange', newWorker.state)
          if (newWorker.state === 'activated') {
            this.isInstalling = false
          }
        })
        // newWorker.state has changed
      })
    })

    if (!window.isCommandRegistered) {
      window.isCommandRegistered = true
      this.$api.addEventListener('command', (evt) => {
        if (!evt.targetUserId || evt.targetUserId == this.user.UserID) {
          if (evt.cmd == 'system-update') {
            this.hasUpdate = true
          } else if (evt.cmd == 'logout') {
            alert(
              'Mohon maaf atas ketidaknyamanannya, karena ada update yang penting, anda diminta untuk login ulang'
            )
            window.location = '/Main/App/Logout'
          } else if (evt.cmd == 'info') {
            alert(evt.text)
          } else if (evt.cmd == 'notification') {
            if (
              window.Notification &&
              window.Notification.permission == 'granted'
            ) {
              const nw = new window.Notification(evt.title, {
                body: evt.text,
              })
              setTimeout(() => {
                nw.close()
              }, 5000)
            }
          }
        }
      })
    }
  },
  methods: {
    updateApp() {
      // eslint-disable-next-line no-self-assign
      // window.location.href = window.location.href
      location.reload(true)
    },
  },
}
</script>
<style lang="scss">
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@400;700&display=swap');

$body-font-family: 'Montserrat';
$title-font: 'Roboto';
html {
  overflow: hidden;
  font-family: $body-font-family, sans-serif;
}
.update-notif {
  padding: 10px;
  text-align: center;
  position: absolute;
  z-index: 3;
  background: orangered;
  width: 100vw;
}
.v-application {
  font-family: $body-font-family, sans-serif !important;
  background-image: url(/imgs/bg.jpg) !important;
  background-size: cover !important;
  background-position: bottom !important;
  font-weight: medium;
  .title {
    // To pin point specific classes of some components
    font-family: $title-font, sans-serif !important;
  }
  [center] {
    text-align: center;
  }
}

.form-inline {
  & > .form-coms {
    display: flex;
    min-width: 340px;
    .form-label {
      flex: 0 0 160px;
      padding: 3px 0;
    }
    .form-coms {
      margin-bottom: 0;
    }
  }
}
.padding {
  padding: 10px;
}

.v-menu__content {
  border-radius: 2px;
  .v-list {
    border-radius: 0;
  }
}
.v-list.v-select-list {
  .v-list-item {
    padding: 0;
    min-height: 10px;
    &:last-child {
      border-bottom: 0px;
    }
    .v-list-item__content {
      padding: 8px 0;
      margin: 0 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      .v-list-item__title {
        font-size: 12px;
        color: #1a1a1a;
      }
    }
  }
}

.v-list.contextmenu {
  padding: 5px;

  .v-list-item {
    padding: 0 10px;
    min-height: 10px;
    &:last-child {
      border-bottom: 0px;
    }
    .v-list-item__icon {
      margin: 12px 2px 10px 0px;
      .v-icon {
        font-size: 14px;
      }
    }
    .v-list-item__title {
      padding: 10px 0;
      font-size: 14px;
    }
  }
}

.forms {
  .v-input {
    display: inline-block;
  }
}
.notices {
  font-family: 'Montserrat', sans-serif !important;
  .toast {
    min-height: 3em !important;
  }
}

.tooltip {
  display: block !important;
  z-index: 10000;
  color: white;
  font-family: 'Montserrat';
  font-size: 12px;

  .tooltip-inner {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px 4px;
    border-radius: 3px;
  }

  .tooltip-arrow {
    width: 0;
    height: 0;
    border-style: solid;
    position: absolute;
    margin: 5px;
    border-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
  }

  &[x-placement^='top'] {
    margin-bottom: 5px;

    .tooltip-arrow {
      border-width: 5px 5px 0 5px;
      border-left-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      bottom: -5px;
      left: calc(50% - 5px);
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &[x-placement^='bottom'] {
    margin-top: 5px;

    .tooltip-arrow {
      border-width: 0 5px 5px 5px;
      border-left-color: transparent !important;
      border-right-color: transparent !important;
      border-top-color: transparent !important;
      top: -5px;
      left: calc(50% - 5px);
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &[x-placement^='right'] {
    margin-left: 5px;

    .tooltip-arrow {
      border-width: 5px 5px 5px 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      border-bottom-color: transparent !important;
      left: -5px;
      top: calc(50% - 5px);
      margin-left: 0;
      margin-right: 0;
    }
  }

  &[x-placement^='left'] {
    margin-right: 5px;

    .tooltip-arrow {
      border-width: 5px 0 5px 5px;
      border-top-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      right: -5px;
      top: calc(50% - 5px);
      margin-left: 0;
      margin-right: 0;
    }
  }

  &.popover {
    $color: #f9f9f9;

    .popover-inner {
      background: $color;
      color: black;
      padding: 24px;
      border-radius: 5px;
      box-shadow: 0 5px 30px rgba(black, 0.1);
    }

    .popover-arrow {
      border-color: $color;
    }
  }

  &[aria-hidden='true'] {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.15s, visibility 0.15s;
  }

  &[aria-hidden='false'] {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.15s;
  }
}
.iblock {
  display: inline-block;
  vertical-align: top;
}

.notices .toast .toast-text {
  padding: 0.5em 1em !important;
}
::-webkit-scrollbar {
  width: 10px; /* for vertical scrollbars */
  height: 10px; /* for horizontal scrollbars */
}

::-webkit-scrollbar-track {
  background: none;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
}

.ui-list {
  .--item {
    border-bottom: 1px solid #ddd;
    padding: 5px 8px;
    font-size: 12px;
    .ordr-1 {
      padding-left: 10px;
      display: flex;
      font-weight: bold;
      & > div {
        flex-grow: 1;
      }
      .status {
        font-weight: normal;
        justify-content: flex-end;
        display: flex;

        .badge {
          padding-left: 9px;
          border-radius: 9px;
        }
      }
    }
    .ordr-2 {
      padding-left: 10px;
      display: flex;

      & > div {
        flex-grow: 1;
      }
      .status {
        justify-content: flex-end;
        display: flex;

        .badge {
          padding-right: 9px;
          padding-left: 9px;
          border-radius: 9px;
        }

        .s-jml {
          min-width: 60px;
          text-align: center;
          background-color: #f3f3f3;
        }

        .s-DISETUJUI {
          background: #4caf50 !important;
          color: white !important;
        }
      }
    }
  }
}

.v-icon {
  &.s-REKOM1 {
    color: #c0ca33 !important;
    background: white !important;
  }
  &.s-REKOM2 {
    color: #ff6f00 !important;
    background: white !important;
  }
  &.s-REKOM3 {
    color: #aa00ff !important;
    background: white !important;
  }
  &.s-REKOM4 {
    color: #9c27b0 !important;
    background: white !important;
  }
  &.s-REKOM5 {
    color: yellow !important;
    background: #333 !important;
  }
  &.s-REKOM6 {
    color: #42a3a7 !important;
    background: white !important;
  }
  &.s-REKOM7 {
    color: #eef7fb !important;
    background: #333 !important;
  }
  &.s-REKOM8 {
    color: #42526e !important;
    background: white !important;
  }
}
.bold {
  font-weight: bold;
}
.gray {
  color: silver;
}
.installing-block {
  width: 100vw;
  height: 100vw;
  position: fixed;
  top: 0;
  /* left: 0; */
  background: white;
  z-index: 11;
}
</style>
