<script>
import { Doughnut, mixins } from 'vue-chartjs'
const { reactiveProp } = mixins

export default {
  extends: Doughnut,
  mixins: [reactiveProp],
  props: ['title', 'options', 'plugins'],
  mounted() {
    if (this.title && this.options?.title) {
      // eslint-disable-next-line vue/no-mutating-props
      this.options.title.text = this.title
    }
    // this.chartData is created in the mixin.
    // If you want to pass options please create a local options object
    this.renderChart(this.chartData, this.options)
  },
}
</script>
