<template>
  <Page title="Monitoring & Evaluasi" :sidebar="true">
    <template v-slot:toolbar>
      <!-- <v-icon @click="doPrint++" v-tooltip="'Download Excel'">
        mdi-microsoft-excel
      </v-icon> -->
    </template>
    <Sidebar
      :value.sync="area"
      :rebind="rebindSidebar"
      :tahun="new Date().getFullYear() - 1"
      :tabs="[2, 4]"
      :filter="filterArea"
    />
    <div style="padding: 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      />
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :filter="filterGrid"
        :autopaging="false"
        :disabled="true"
        :doPrint="doPrint"
        :doRebind="doRebind"
        height="calc(100vh - 260px)"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'RK',
            value: 'Tahapan',
            class: 'center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'Prior',
            value: 'Prioritas',
            class: 'center',
          },
          {
            name: 'DT',
            value: 'TipeData',
          },
          {
            name: '',
            value: 'Dokumen',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Checklist',
            class: 'plain center',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox :value.sync="row.IsSelected" checkedIcon="check_box" />
        </template>
        <template v-slot:row-Tahapan="{ row }">
          <v-icon v-tooltip="row.Tahapan">
            mdi-numeric-{{
              row.Tahapan ? row.Tahapan.replace('REKOM ', '') : ''
            }}-box
          </v-icon>
        </template>
        <template v-slot:row-NIK="{ row }">
          <v-btn
            text
            small
            color="primary"
            @click="OpenDetail(row.NoRef)"
            style="width: 165px; text-align: left"
          >
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
        </template>
        <template v-slot:row-Prioritas="{ row }">
          <v-icon
            :color="priorcolors[row.Prioritas]"
            v-tooltip="`Prioritas ${row.Prioritas}`"
          >
            mdi-numeric-{{ row.Prioritas }}-circle
          </v-icon>
        </template>
        <template v-slot:row-TipeData="{ row }">
          {{ parseInt((row.TipeData + '').substr(-2)) + 2000 }}
        </template>
        <template v-slot:row-Checklist="{ row }">
          <v-icon v-if="row.IsComplete" @click="OpenChecklist(row.NIK)">
            mdi-clipboard-list
          </v-icon>
        </template>
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ChecklistMonev :nik="selectedNIK" :show.sync="showChecklistModal" />
    </div>
  </Page>
</template>
<script>
import Sidebar from '../InputUsulan/SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import ChecklistMonev from './Checklist.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ChecklistMonev,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showGantiPenerima: false,
    selectedRef: null,
    selectedNIK: null,
    doPrint: 0,
    doRebind: 0,
    rebindSidebar: 0,
    filterArea: {
      IsApproved: 1,
    },

    IsRekom: false,
    NoRefs: ',',
  }),
  computed: {
    dbref() {
      return 'BLG.ProposalDet'
    },
    areaParams() {
      let { Kabupaten, Kecamatan, Kelurahan } = this.area
      return { Kabupaten, Kecamatan, Kelurahan }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue
      }).length
    },
    buttonStatus() {
      return this.datagrid.reduce((total, curr) => {
        if (curr.IsSelected && curr.Tahapan) total['IsCancel'] = true
        if (curr.IsSelected && !curr.Tahapan) total['IsRekom'] = true
        return total
      }, {})
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    datagrid(val) {
      this.IsRekom = false
      this.NoRefs = ','
      if (val && val.length) {
        val.forEach((v) => {
          if (v.IsApproved == 2) this.IsRekom = true
          if (v.IsApproved) this.NoRefs += v.NoRef + ','
        })
      }
    },
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenChecklist(nik) {
      this.selectedNIK = nik
      this.showChecklistModal = true
    },
    OpenGantiPenerima(nik) {
      this.selectedNIK = nik
      this.showGantiPenerima = true
    },
    filterGrid(row) {
      return Boolean(row.IsApproved)
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
