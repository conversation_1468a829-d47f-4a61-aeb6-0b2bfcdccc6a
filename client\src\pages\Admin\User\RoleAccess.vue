<template>
  <div style="width: 355px">
    <Grid
      :datagrid.sync="roles"
      dbref="Arch.RoleAccess"
      :dbparams="dbparams"
      :disabled="true"
      style="height: calc(100vh - 235px)"
      class="dense page-role-access"
      :columns="[
        {
          name: 'Menu',
          value: 'MenuName',
          width: '220px',
        },
        {
          name: 'Access',
          value: 'AllowAccess',
        },
      ]"
    >
      <template v-slot:row-MenuName="{ row }">
        <div :class="'menu-' + row.MenuID.length">
          {{ row.MenuName }}
        </div>
      </template>
      <template v-slot:row-AllowAccess="{ row }">
        <div>
          <Checkbox
            :value.sync="row.AllowAccess"
            checkedIcon="mdi-eye"
            checkedColor="primary"
            @click="ChangeAccess(row)"
          />
          <Checkbox
            v-show="row.MenuID.length > 3"
            :value.sync="row.AllowWrite"
            checkedIcon="mdi-pencil"
            checkedColor="success"
            color="primary"
            style="margin-left: 5px"
          />
          <Checkbox
            v-show="row.MenuID.length > 3"
            :value.sync="row.AllowDelete"
            checkedIcon="mdi-trash-can"
            checkedColor="error"
            style="margin-left: 5px"
            @click="ChangeDelete(row)"
          />
        </div>
      </template>
    </Grid>
  </div>
</template>
<script>
export default {
  data: () => ({
    roles: null,
    forms: {},
  }),
  props: {
    roleId: [String, Number],
    'page-data': Array,
  },
  async mounted() {},
  watch: {
    roles(val) {
      this.$emit('update:page-data', val)
    },
  },
  computed: {
    dbparams() {
      return {
        RolePositionID: this.roleId,
      }
    },
  },
  methods: {
    ShowRoleAccess() {
      this.roleAccess = true
    },
    ChangeAccess(row) {
      if (!row.AllowAccess) {
        row.AllowWrite = false
        row.AllowDelete = false
      }
    },
    ChangeDelete(row) {
      if (row.AllowDelete) {
        row.AllowWrite = true
        row.AllowAccess = true
      }
    },
  },
}
</script>
<style lang="scss">
.page-role-access {
  .menu-3 {
    font-weight: bold;
  }
  .menu-5 {
    padding-left: 10px;
  }
  .menu-7 {
    padding-left: 20px;
    color: gray;
  }
}
</style>
