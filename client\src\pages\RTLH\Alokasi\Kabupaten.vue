<template>
  <div>
    <div style="display:flex;">
      <XSelect
        dbref="PRM.SelProposal"
        :value.sync="proposal"
        :valueAsObject="true"
        width="80px"
        style="margin-right:10px;"
      />
      <Checkbox
        :value.sync="hasAllocation"
        style="margin-left:10px; margin-top:2px;"
        text="Mempunyai Alokasi"
      />
    </div>
    <Grid
      dbref="PRM.AlokasiKab"
      :dbparams="params"
      :datagrid.sync="forms.XmlAlokasi"
      :filter="filterGrid"
      :disabled="true"
      :autopaging="false"
      height="calc(100vh - 240px)"
      :columns="[
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          width: '150px',
        },
        {
          name: '<PERSON>ota',
          value: 'Kuota',
          class: 'plain',
        },
        {
          name: 'Terpakai',
          value: 'Terpakai',
          class: 'plain',
        },
        {
          name: '',
          value: '<PERSON>a',
          class: 'plain',
        },
      ]"
    >
      <template v-slot:row-Kuota="{ row }">
        <XInput type="number" :value.sync="row.Kuota" width="80px" />
      </template>
      <template v-slot:row-Terpakai="{ row }">
        <div
          style="text-align:right; padding-right:8px;"
          :style="{
            color:
              row.Terpakai > row.Kuota
                ? 'red'
                : row.Terpakai == row.Kuota
                ? 'blue'
                : 'black',
          }"
        >
          {{ row.Terpakai }}
        </div>
      </template>
      <template v-slot:row-Desa="{ row }">
        <v-btn x-small text color="primary" @click="OpenDesa(row)">
          DESA
          <v-icon x-small right>mdi-open-in-new</v-icon>
        </v-btn>
      </template>
    </Grid>
    <br />
    <v-btn color="primary" @click="Save">
      SIMPAN
    </v-btn>
    <ModalDesa :show.sync="showDesa" :params="selected" />
  </div>
</template>
<script>
import ModalDesa from './ModalDesa.vue'
export default {
  components: { ModalDesa },
  data: () => ({
    proposal: {
      InputName: new Date().getFullYear(),
    },
    forms: {
      XmlAlokasi: [],
    },
    hasAllocation: false,
    showDesa: false,
    selected: {},
  }),
  computed: {
    params() {
      let { InputName } = this.proposal
      return { Tahun: InputName }
    },
  },
  methods: {
    async Save() {
      await this.$api.call('PRM_SavAlokasiKab', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
    OpenDesa(row) {
      this.selected = { ...row, Tahun: this.proposal.InputName, Sumber: 2 }
      this.showDesa = true
    },
  },
}
</script>
<style lang="scss"></style>
