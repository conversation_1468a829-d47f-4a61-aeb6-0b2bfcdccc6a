<template>
  <Modal
    :title="title"
    :show.sync="xshow"
    width="500px"
    @onSubmit="Save"
    :submitText="submitText"
  >
    <div
      class="form-inline"
      :class="{ transparent: loading }"
      :style="isMobile ? 'margin-left:-15px' : 'display:flex;'"
    >
      <div>
        <!-- <Uploader
          :value.sync="forms['Termin' + termin]"
          accept=".pdf"
          v-if="!isMobile"
        >
        </Uploader> -->
        <div>
          <v-tabs :vertical="!isMobile" show-arrows v-if="xshow">
            <v-tab @click="tab = 'umum'" left>
              <v-icon left v-if="completion['umum']"> mdi-check-circle </v-icon>
              <v-icon left v-else> mdi-circle-outline </v-icon>
              <div>
                <div style="font-size: smaller">Umum</div>
                <div>Checklist</div>
              </div>
            </v-tab>
            <v-tab @click="tab = 'checkruspin'" left>
              <v-icon left v-if="completion['ruspin']">
                mdi-check-circle
              </v-icon>
              <v-icon left v-else> mdi-circle-outline </v-icon>
              <div>
                <div style="font-size: smaller">Ruspin</div>
                <div>Checklist</div>
              </div>
            </v-tab>
            <v-tab @click="tab = 'fotoruspin'" left>
              <v-icon left v-if="completion['fotoruspin']">
                mdi-check-circle
              </v-icon>
              <v-icon left v-else> mdi-circle-outline </v-icon>
              <div>
                <div style="font-size: smaller">Ruspin</div>
                <div>Dokumentasi</div>
              </div>
            </v-tab>
            <v-tab @click="tab = 'checktokmat'" left>
              <v-icon left v-if="completion['tokmat']">
                mdi-check-circle
              </v-icon>
              <v-icon left v-else> mdi-circle-outline </v-icon>
              <div>
                <div style="font-size: smaller">Material</div>
                <div>Checklist</div>
              </div>
            </v-tab>
            <v-tab @click="tab = 'fototokmat'" left>
              <v-icon left v-if="completion['fototokmat']">
                mdi-check-circle
              </v-icon>
              <v-icon left v-else> mdi-circle-outline </v-icon>
              <div>
                <div style="font-size: smaller">Material</div>
                <div>Dokumentasi</div>
              </div>
            </v-tab>
          </v-tabs>
        </div>
      </div>
      <div style="width: 450px" v-show="tab == 'umum'">
        <div style="padding: 5px">
          <div style="display: flex">
            <CheckUpload
              :value.sync="umum.CekRPD"
              label="RPD"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="DownloadRPD"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="umum.CekPermohonanPembayaran"
              label="Permohonan Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('2_SURAT_PERMOHONAN_PEMBAYARAN_TERMIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="umum.CekBAPembayaran"
              label="BA Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('3_BA_PEMBAYARAN_TERMIN_POKMAS_KPA.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="umum.CekPenggunaanDana"
              label="Pernyataan Penggunaan Dana"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('4_SURAT_PERNYATAAN_PENGGUNAAN_DANA.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekPaktaIntegritas"
              label="Pakta Integritas Ruspin"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('1_PAKTA_INTEGRITAS_PENYEDIA_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekPaktaIntegritas"
              label="Pakta Integritas Tokmat"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('1_PAKTA_INTEGRITAS_PENYEDIA_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
        </div>
      </div>
      <div style="width: 450px" v-show="tab == 'checkruspin'">
        <div style="padding: 5px">
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekPermohonanPemeriksaan"
              label="Permohonan Pemeriksaan"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('2_PERMOHONAN_PEMERIKSAAN_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekBAPemeriksaan"
              label="BA Pemeriksaan"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('3_BA_PEMERIKSAAN_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekDaftarKwantitas"
              label="Daftar Kwantitas"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('4_DAFTAR_KWANTITAS_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekBASerahTerima"
              label="BA Serah Terima"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('5_BA_SERAH_TERIMA_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekKwitansi"
              label="Kwitansi"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('Kwitansi.ods')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekPermohonanTagihan"
              label="Permohonan Tagihan"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('7_SURAT_PERMOHONAN_TAGIHAN_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekPermohonanPembayaran"
              label="Permohonan Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="
                Download(
                  '8_PERMOHONAN_PEMBAYARAN_DARI_PENYEDIA_KE_POKMAS_RUSPIN.docx'
                )
              "
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekBAPembayaran"
              label="BA Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="
                Download('9_BA_PEMBAYARAN_POKMAS_KE_PENYEDIA_RUSPIN.docx')
              "
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekSPK"
              label="SPK"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('10_SPK_RUSPIN.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="ruspin.CekDO"
              label="DO"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
          </div>
        </div>
      </div>
      <div style="width: 450px" v-show="tab == 'checktokmat'">
        <div style="padding: 5px">
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekPermohonanPemeriksaan"
              label="Permohonan Pemeriksaan"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('2_PERMOHONAN_PEMERIKSAAN_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekBAPemeriksaan"
              label="BA Pemeriksaan"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('3_BA_PEMERIKSAAN_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekDaftarKwantitas"
              label="Daftar Kwantitas"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('4_DAFTAR_KWANTITAS_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekBASerahTerima"
              label="BA Serah Terima"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('5_BA_SERAH_TERIMA_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekKwitansi"
              label="Kwitansi"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('Kwitansi.ods')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekPermohonanTagihan"
              label="Permohonan Tagihan"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('7_SURAT_PERMOHONAN_TAGIHAN_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekPermohonanPembayaran"
              label="Permohonan Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="
                Download(
                  '8_PERMOHONAN_PEMBAYARAN_DARI_PENYEDIA_KE_POKMAS_TOKMAT.docx'
                )
              "
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekBAPembayaran"
              label="BA Pembayaran"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="
                Download('9_BA_PEMBAYARAN_POKMAS_KE_PENYEDIA_TOKMAT.docx')
              "
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekSPK"
              label="SPK"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
            <v-icon
              color="primary"
              small
              v-tooltip="'download template'"
              @click="Download('10_SPK_TOKMAT.docx')"
            >
              mdi-download
            </v-icon>
          </div>
          <div style="display: flex">
            <CheckUpload
              :value.sync="tokmat.CekDO"
              label="DO"
              accept=".pdf"
            ></CheckUpload>
            <v-spacer />
          </div>
        </div>
      </div>
      <div class="iblock" style="width: 550px" v-show="tab == 'fotoruspin'">
        <Grid
          :datagrid.sync="ruspins"
          dbref="RLK.Material"
          :dbparams="paramsus"
          :mobile="true"
          width="100%"
          :columns="[
            {
              name: 'Material',
              value: 'Material',
              width: '200px',
              editable: {
                com: 'XInput',
              },
            },
            {
              name: 'Jml',
              value: 'Jml',
              editable: {
                com: 'XInput',
                type: 'number',
              },
            },
            {
              name: 'Satuan',
              value: 'Satuan',
              editable: {
                com: 'XInput',
              },
            },
            {
              name: 'Foto 1',
              value: 'Foto1',
              class: 'center',
              editable: {
                com: 'Uploader',
                mode: 'icon',
                accept: '.jpg,.png',
              },
            },
            {
              name: 'Foto 2',
              value: 'Foto2',
              class: 'center',
              editable: {
                com: 'Uploader',
                mode: 'icon',
                accept: '.jpg,.png',
              },
            },
          ]"
        >
          <template v-slot:row-Foto1="{ row }">
            <v-icon
              v-if="row.Foto1"
              color="primary"
              v-tooltip="{
                autoHide: false,
                classes: 'img-no-arrow',
                content: `<div style='background-image:url(${
                  $api.url + row.Foto1
                });'></div>`,
              }"
              @click="openImage(row.Foto1, '_blank')"
            >
              mdi-image
            </v-icon>
            <v-icon v-else color="silver">mdi-image-off-outline</v-icon>
          </template>
          <template v-slot:row-Foto2="{ row }">
            <v-icon
              v-if="row.Foto2"
              color="primary"
              v-tooltip="{
                autoHide: false,
                classes: 'img-no-arrow',
                content: `<div style='background-image:url(${
                  $api.url + row.Foto2
                });'></div>`,
              }"
              @click="openImage(row.Foto2, '_blank')"
            >
              mdi-image
            </v-icon>
            <v-icon v-else color="silver">mdi-image-off-outline</v-icon>
          </template>
        </Grid>
      </div>
      <div class="iblock" style="width: 550px" v-show="tab == 'fototokmat'">
        <Grid
          :datagrid.sync="materials"
          dbref="RLK.Material"
          :dbparams="params"
          :mobile="true"
          width="100%"
          :columns="[
            {
              name: 'Material',
              value: 'Material',
              width: '200px',
              editable: {
                com: 'XInput',
              },
            },
            {
              name: 'Jml',
              value: 'Jml',
              editable: {
                com: 'XInput',
                type: 'number',
              },
            },
            {
              name: 'Satuan',
              value: 'Satuan',
              editable: {
                com: 'XInput',
              },
            },
            {
              name: 'Foto 1',
              value: 'Foto1',
              class: 'center',
              editable: {
                com: 'Uploader',
                mode: isMobile ? '' : 'icon',
                accept: '.jpg,.png',
              },
            },
            {
              name: 'Foto 2',
              value: 'Foto2',
              class: 'center',
              editable: {
                com: 'Uploader',
                mode: isMobile ? '' : 'icon',
                accept: '.jpg,.png',
              },
            },
          ]"
        >
          <template v-slot:row-Foto1="{ row }">
            <v-icon
              v-if="row.Foto1"
              color="primary"
              v-tooltip="{
                autoHide: false,
                classes: 'img-no-arrow',
                content: `<div style='background-image:url(${
                  $api.url + row.Foto1
                });'></div>`,
              }"
              @click="openImage(row.Foto1, '_blank')"
            >
              mdi-image
            </v-icon>
            <v-icon v-else color="silver">mdi-image-off-outline</v-icon>
          </template>
          <template v-slot:row-Foto2="{ row }">
            <v-icon
              v-if="row.Foto2"
              color="primary"
              v-tooltip="{
                autoHide: false,
                classes: 'img-no-arrow',
                content: `<div style='background-image:url(${
                  $api.url + row.Foto2
                });'></div>`,
              }"
              @click="openImage(row.Foto2, '_blank')"
            >
              mdi-image
            </v-icon>
            <v-icon v-else color="silver">mdi-image-off-outline</v-icon>
          </template>
        </Grid>
      </div>
    </div>
    <template v-slot:left-action>
      <!-- <div style="padding-left: 10px; color: gray" v-show="tab == 'checklist'">
        <Checkbox text="Check Semua" :value.sync="checkAll" />
      </div> -->
      <v-icon v-if="loading"> mdi-spin mdi-loading </v-icon>
      <div
        style="padding-left: 10px; color: gray"
        v-show="['fototokmat', 'fotoruspin'].includes(tab)"
      >
        <v-btn small text color="primary" @click="GenerateEXCEL">
          <v-icon left>mdi-microsoft-excel</v-icon>
          DOWNLOAD EXCEL
        </v-btn>
      </div>
    </template>
  </Modal>
</template>
<script>
// import { mapGetters } from 'vuex'
import CheckUpload from './CheckUpload.vue'
export default {
  components: {
    CheckUpload,
  },
  data: () => ({
    xshow: false,
    forms: {},
    umum: {},
    ruspin: {},
    tokmat: {},
    checkAll: false,
    tab: 'umum',
    completion: {
      umum: true,
      ruspin: true,
      tokmat: true,
      fotoruspin: true,
      fototokmat: true,
    },
    materials: [],
    ruspins: [],
    loading: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
    kodedagri: [String, Number],
    termin: [String, Number],
    tahun: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) {
        this.tab = 'umum'
        this.umum = {}
        this.ruspin = {}
        this.tokmat = {}
        this.materials = []
        this.ruspins = []
        this.completion = {
          umum: true,
          ruspin: true,
          tokmat: true,
          fotoruspin: true,
          fototokmat: true,
        }
      } else {
        this.populate()
      }
      this.$emit('update:show', val)
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x.match(/^Cek/)) this.forms[x] = val
      }
    },
    materials(vals) {
      this.completion.fototokmat = true
      if (!vals || !vals.length) {
        this.completion.fototokmat = false
        return
      }
      for (let m of vals) {
        if (!m.Foto1 || !m.Foto2) {
          this.completion.fototokmat = false
          break
        }
      }
    },
    ruspins(vals) {
      this.completion.fotoruspin = true
      if (!vals || !vals.length) {
        this.completion.fotoruspin = false
        return
      }
      for (let m of vals) {
        if (!m.Foto1 || !m.Foto2) {
          this.completion.fotoruspin = false
          break
        }
      }
    },
  },
  computed: {
    title() {
      return 'CHECKLIST TERMIN ' + this.termin
    },
    submitText() {
      return ['umum', 'checkruspin', 'checktokmat'].includes(this.tab)
        ? 'SIMPAN'
        : ''
      // return 'SIMPAN'
    },
    params() {
      return {
        NIK: this.nik,
        Termin: this.termin,
        Tahun: this.tahun,
        Jenis: 'mat',
      }
    },
    paramsus() {
      return {
        NIK: this.nik,
        Termin: this.termin,
        Tahun: this.tahun,
        Jenis: 'ruspin',
      }
    },
  },
  methods: {
    async populate() {
      // this.forms = {}
      // let { data } = await this.$api.call('RLK.SelPencairanCek23', {
      //   NIK: this.nik,
      //   Termin: this.termin,
      //   Tahun: this.tahun,
      // })
      // if (data.length) this.forms = data[0]
      // for (let k in this.forms) {
      //   if (k.match(/T2$/) && !this.forms[k]) this.completion[0] = false
      // }
      this.loading = true
      let { data } = await this.$api.call('RLK.SelBerkas', {
        NIK: this.nik,
        Termin: this.termin,
        Tahun: this.tahun,
        KodeDagri: this.kodedagri,
      })

      for (let k in this.umum) {
        if (data.umum?.[k]?.Url) this.umum[k] = data.umum[k].Url
        else this.completion.umum = false
      }
      for (let d of data) {
        if (this[d.GroupId]) {
          this[d.GroupId]['Cek' + d.Tipe] = d.Url
          if (!d.Url) {
            this.completion[d.GroupId] = false
          }
        }
      }

      this.CheckCompletion('umum')
      this.CheckCompletion('ruspin')
      this.CheckCompletion('tokmat')
      this.loading = false
    },
    CheckCompletion(groupId) {
      this.completion[groupId] = true
      console.log('CheckCompletion', groupId)
      if (!Object.keys(this[groupId]).length) {
        this.completion[groupId] = false
        console.log('fail 1')
        return
      }
      for (let k in this[groupId]) {
        if (!this[groupId][k]) {
          this.completion[groupId] = false
          console.log('fail 2', k)
          break
        }
      }
    },
    PutData(data, groupId, obj) {
      for (let k in obj) {
        data.push({
          // NIK: groupId == 'umum' ? '0' : this.nik,
          NIK: this.nik,
          KodeDagri: this.kodedagri,
          Tahun: this.tahun,
          Termin: this.termin,
          GroupId: groupId,
          Tipe: k.replace(/^Cek/, ''),
          Url: obj[k],
        })
      }
    },
    async Save() {
      // let ret = await this.$api.call('RLK.SavPencairanCek23', {
      //   ...this.forms,
      //   NIK: this.nik,
      //   Tahun: this.tahun,
      // })
      // if (ret.success) this.$emit('update:show', false)

      let data = []
      this.PutData(data, 'umum', this.umum)
      this.PutData(data, 'ruspin', this.ruspin)
      this.PutData(data, 'tokmat', this.tokmat)

      let ret = await this.$api.call('RLK.SavBerkas', {
        JsonBerkas: data,
      })
      if (ret.success) this.$emit('update:show', false)
    },
    DownloadRPD() {
      window.open(
        this.$api.url +
          '/report/backlog/doc/RPD.ods?out=xlsx&Tahun=' +
          this.tahun +
          '&KodeDagri=' +
          this.kodedagri
      )
    },
    Download(params) {
      window.open(
        this.$api.url +
          '/report/backlog/doc/' +
          params +
          '?NIK=' +
          this.nik +
          '&sp=RLK_RptDocPencairan'
      )
    },
    openImage(imgUrl) {
      window.open(this.$api.url + imgUrl, '_blank')
    },
    async GenerateEXCEL() {
      let ret = await this.$api.post(
        '/reports/template/PencairanRelokasi.xlsx',
        {
          NIK: this.nik,
          Termin: this.termin,
          Tahun: this.tahun,
          Jenis: this.tab == 'fototokmat' ? 'mat' : 'ruspin',
          CurrPageUrl: window.location.pathname,
          // imgcols: ['FotoRumah0', 'FotoRumah50', 'FotoRumah100'],
          hasImage: true,
          sp: 'RLK_RptProfilPencairan',
          transformResult: {
            material: 1,
          },
        }
      )
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
.img-no-arrow {
  .tooltip-arrow {
    display: none;
  }
  .tooltip-inner {
    div {
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      width: 150px;
      height: 150px;
    }
  }
}
.v-tab[left] {
  justify-content: left;
  div {
    text-align: left;
  }
}
.transparent {
  opacity: 0;
}
</style>
