<template>
  <div style="height: 100%; max-width: 100vw">
    <div class="quarter-chart">
      <HorizontalBarChart
        :chart-data="datacollection"
        :options="chartOptions"
        class="quarterly-chart"
      ></HorizontalBarChart>
    </div>
  </div>
</template>
<script>
import HorizontalBarChart from '../../components/Charts/HorizontalBar.vue'

export default {
  components: {
    HorizontalBarChart,
  },
  data: () => ({
    progress: {},
    datacollection: { datasets: [] },
    chartOptions: {
      indexAxis: 'y',
      title: {
        display: true,
        text: 'Progress Triwulan',
        fontSize: 20,
      },
      legend: {
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
      },
      responsive: true,
      maintainAspectRatio: false,
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            var label = data.datasets[tooltipItem.datasetIndex].label || ''

            if (label) {
              label += ': '
            }
            label += Math.round(tooltipItem.xLabel * 100) / 100
            return label
          },
          footer(items) {
            let t = 0
            for (let item of items) {
              t += item.xLabel
            }
            let x = t - items[items.length - 1].xLabel
            return (
              'Target: ' +
              t +
              '\nProgress: ' +
              x +
              ' (' +
              ((x / t) * 100).toFixed(2) +
              '%)'
            )
          },
        },
      },
    },
  }),
  props: {
    tipeData: [Number, String],
  },
  watch: {
    tipeData() {
      this.populateProgress()
      this.populateChart()
    },
  },
  created() {
    this.populateProgress()
    this.populateChart()
  },
  methods: {
    async populateProgress() {
      let { data } = await this.$api.call('PRM.SelChartProgress', {
        TipeData: this.tipeData,
      })
      this.progress = data[0]
      this.progress.TotalIntervensi =
        0 +
        data[0].APBN +
        data[0].APBD1 +
        data[0].APBD2 +
        data[0].CSR +
        data[0].Lain +
        data[0].Validasi +
        data[0].DiluarPrioritas +
        data[0].BebasPBDT +
        data[0].Meninggal +
        data[0].DoubleSalahData

      this.progress.SisaPBDT = data[0].TotalData - this.progress.TotalIntervensi
    },
    async populateChart() {
      let { data } = await this.$api.call('PRM.SelChartQuarterly', {
        TipeData: this.tipeData,
      })
      let labels = []
      let colors = {
        Q1: '#58508d',
        Q2: '#bc5090',
        Q3: '#ff6361',
        Q4: '#ffa600',
        Sisa: '#ddd',
      }
      let ds = []
      data.forEach((d) => {
        labels.push(d.Kabupaten)
        let i = 0
        Object.keys(d).forEach((k) => {
          if (colors[k]) {
            ds[i] = ds[i] || {
              data: [],
              baseColor: colors[k],
              pointStyle: 'rectRot',
              pointRadius: 3,
            }
            ds[i].label = k.replace('_', ' ')
            ds[i].stack = k.match(/ALL$/) ? 2 : 1
            ds[i].data.push(d[k])
            i++
          }
        })
      })
      this.datacollection = {
        labels: labels,
        datasets: ds || [],
      }
    },
    randomScalingFactor() {
      return Math.floor(Math.random() * (50 - 5 + 1)) + 5
    },
  },
}
</script>
<style lang="scss">
.quarter-chart {
  height: 85vh;
  background: white;
}
.quarterly-chart {
  height: 100% !important;
  width: 100% !important;
  canvas {
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
