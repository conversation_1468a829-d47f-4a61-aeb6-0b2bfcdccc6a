{"name": "client", "version": "0.0.0", "license": "ISC", "scripts": {"start": "vite --host --port 8000", "denorun": "deno run --allow-env --allow-read --allow-sys --allow-ffi --allow-run --allow-write --allow-net npm:vite --host --port 8000", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.6.7", "chart.js": "2", "exif-js": "^2.3.0", "exifr": "^7.1.3", "material-design-icons-iconfont": "^5.0.1", "moment": "^2.30.1", "ol": "^6.15.1", "unplugin-vue-components": "^0.26.0", "v-click-outside": "^3.2.0", "v-tooltip": "^2.1.3", "vue": "^2.7.7", "vue-chartjs": "3", "vue-router": "^3.6.5", "vue-toast-notification": "^0.6.3", "vuetify": "^2.7.1", "vuex": "^3.6.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.0", "@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue2": "^1.1.2", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "sass": "^1.70.0", "terser": "^5.14.2", "vite": "^3.0.2", "vite-plugin-pwa": "^0.13.3"}}