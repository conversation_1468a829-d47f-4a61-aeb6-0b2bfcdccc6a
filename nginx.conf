events {
	worker_connections 768;
	# multi_accept on;
}
http {
	server {
		listen 		  80 default_server;
		listen 		  [::]:80 default_server;
        # root          /frontend;

		# # Server
		location /api {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://backend:8001;
		}
		location /report {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://backend:8001;
		}
		location /reports {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://backend:8001;
		}
		location /Main/App/Extender {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://backend:8001;
		}
		# location / {
		# 	proxy_set_header Host $host;
		# 	proxy_set_header X-Real-IP $remote_addr;
		# 	proxy_pass http://frontend;
		# }
		# location ~ ^/(static) {
		# 	proxy_set_header Host $host;
		# 	proxy_set_header X-Real-IP $remote_addr;
		# 	proxy_pass http://frontend;
		# }
	}
}