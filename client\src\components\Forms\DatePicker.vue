<template>
  <div class="form-coms ui-datepicker">
    <v-menu
      v-model="showPopup"
      :close-on-content-click="false"
      transition="scale-transition"
      offset-y
      min-width="290px"
      min-height="290px"
      class="ui-datepicker-menu"
      :disabled="disabled"
    >
      <template v-slot:activator="{ on }">
        <XInput
          type="text"
          :label="label"
          right-icon="mdi-calendar"
          :value.sync="formattedValue"
          v-on="on"
          :disabled="disabled"
        />
      </template>
      <v-date-picker
        v-model="vmodel"
        @input="showPopup = false"
      ></v-date-picker>
    </v-menu>
  </div>
</template>
<script>
import moment from 'moment'

export default {
  data: () => ({
    showPopup: false,
    vmodel: null,
  }),
  computed: {
    formattedValue: {
      get() {
        if (this.vmodel) return moment(this.vmodel).format('DD-MMM-YYYY')
        else return ''
      },
      set(val) {
        if (val.match(/\d?\d-\w{3}-\d{4}/) && moment(val)) {
          this.vmodel = moment(val).format('YYYY-MM-DD')
        }
      },
    },
  },
  mounted() {
    if (this.useDefault || this.value)
      this.vmodel = moment(this.value).format('YYYY-MM-DD')
  },
  watch: {
    value(val) {
      if (val) this.vmodel = moment(val).format('YYYY-MM-DD')
      else this.vmodel = null
    },
    vmodel(val) {
      // console.log('vmodel', val)
      this.$emit('update:value', val)
      this.$emit('change', val)
    },
  },
  props: {
    label: String,
    value: [String, Object, Date],
    useDefault: Boolean,
    disabled: Boolean,
  },
}
</script>
<style lang="scss">
.form-inline {
  .ui-datepicker {
    .v-menu__content {
      margin-left: 160px;
    }
    .ui-input {
      display: flex;
      width: 100%;
    }
  }
}
.ui-datepicker {
  margin-bottom: 8px;
}
.v-picker__title {
  display: none;
}
.v-date-picker-table {
  height: 222px;
}
</style>
