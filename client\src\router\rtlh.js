export default [
  {
    path: '/Main/RTLH/PBDT',
    component: () => import('../pages/RTLH/Database/Database.vue'),
  },
  {
    path: '/Main/RTLH/InputProposal',
    component: () => import('../pages/RTLH/InputUsulan/InputUsulan.vue'),
  },
  {
    path: '/Main/RTLH/ReviewProposal',
    component: () => import('../pages/RTLH/ReviewUsulan/ReviewUsulan.vue'),
  },
  {
    path: '/Main/RTLH/FinalisasiUsulan',
    component: () =>
      import('../pages/RTLH/FinalisasiUsulan/FinalisasiUsulan.vue'),
  },
  {
    path: '/Main/RTLH/InputBerkas',
    component: () => import('../pages/RTLH/InputBerkas/InputBerkas.vue'),
  },
  {
    path: '/Main/RTLH/RekomProposal',
    component: () => import('../pages/RTLH/Rekomendasi/Rekomendasi.vue'),
  },
  {
    path: '/Main/RTLH/PBDT_edit',
    component: () => import('../pages/RTLH/ValidasiData/ValidasiData.vue'),
  },
  {
    path: '/Main/RTLH/Alokasi',
    component: () => import('../pages/RTLH/Alokasi/Alokasi.vue'),
  },
  {
    path: '/Main/RTLH/AlokasiDewan',
    component: () => import('../pages/RTLH/Alokasi/Dewan.vue'),
  },
  {
    path: '/Main/RTLH/Monev',
    component: () => import('../pages/RTLH/Monitoring/Monitoring.vue'),
  },
  {
    path: '/Main/RTLH/LPJ',
    component: () => import('../pages/RTLH/LPJ/LPJ.vue'),
  },
  {
    path: '/Main/RTLH/Report',
    component: () => import('../pages/RTLH/Report.vue'),
  },
  {
    path: '/Main/RTLH/DesaBinaan',
    component: () => import('../pages/RTLH/DesaBinaan.vue'),
  },
  {
    path: '/Main/RTLH/SkGub',
    component: () => import('../pages/RTLH/SkGub/Alokasi.vue'),
  },
  {
    path: '/Main/RTLH/UsulanIntervensi',
    component: () => import('../pages/RTLH/Intervensi/Intervensi.vue'),
  },
  {
    path: '/Main/RTLH/ReviewKabupaten',
    component: () =>
      import('../pages/RTLH/ReviewKabupaten/ReviewKabupaten.vue'),
  },
]
