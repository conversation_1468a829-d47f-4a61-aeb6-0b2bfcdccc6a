require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  port: parseInt(process.env.DB_PORT || '3307'),
  acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

// Function to get a connection with retry logic
const getConnection = () => {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error('Error getting MySQL connection:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Retrying connection in 2 seconds...');
          setTimeout(() => {
            getConnection().then(resolve).catch(reject);
          }, 2000);
        } else {
          reject(err);
        }
      } else {
        // Set session variables to increase timeouts
        connection.query('SET SESSION wait_timeout=300', (err) => {
          if (err) console.error('Error setting wait_timeout:', err);

          connection.query('SET SESSION interactive_timeout=300', (err) => {
            if (err) console.error('Error setting interactive_timeout:', err);
            resolve(connection);
          });
        });
      }
    });
  });
};

// Function to execute a query with retry logic
const executeQuery = async (sql, params, retries = 3) => {
  let connection;
  try {
    connection = await getConnection();
    const query = util.promisify(connection.query).bind(connection);
    return await query(sql, params);
  } catch (error) {
    if (retries > 0 && (error.code === 'PROTOCOL_CONNECTION_LOST' ||
                         error.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR' ||
                         error.message.includes('timeout'))) {
      console.log(`Query failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return executeQuery(sql, params, retries - 1);
    }
    throw error;
  } finally {
    if (connection) connection.release();
  }
};

// Process records in batches
async function run(row) {
  
  // if (row.Kabupaten < 'KOTA MAGELANG') return

  console.log(row.Kabupaten)
  
  const records = await executeQuery(`select NIK
      from prm_pbdt pp 
      where KepemilikanRumah = 1 AND Tahun IS NULL AND TipeData = 115
      AND Prioritas = 3
      AND Kabupaten = '${row.Kabupaten}'
      LIMIT 13`)
  
  const NIKS = records.map(r => r.NIK).join(',')
  // console.log(NIKS)
  
  await executeQuery(`UPDATE prm_pbdt SET Sumber = 8, StatusID = 50, Tahun = 2025, Remarks = 'OUT MIKO'
    WHERE Tahun IS NULL AND TipeData = 115 AND KepemilikanRumah = 1
    AND NIK IN (
      ${NIKS}
    )
    AND Kabupaten = '${row.Kabupaten}'`);
  // return results;
}

async function main() {
  try {
    
    //const records = await executeQuery('select AreaName Kabupaten from arch_area aa where ParentAreaID = 33');

    const records = [{Kabupaten:'CILACAP'}]
    // console.log(records)
    for(const row of records) {
      await run(row)
    }
    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted, closing connections...');
  pool.end(err => {
    if (err) console.error('Error closing MySQL pool:', err);
    process.exit(2);
  });
});

main();