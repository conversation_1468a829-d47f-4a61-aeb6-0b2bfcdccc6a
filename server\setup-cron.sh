#!/bin/bash

# This script sets up a cron job to run the cleanup script every 6 hours

# Get the current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Create a temporary crontab file
TEMP_CRONTAB=$(mktemp)

# Export current crontab
crontab -l > "$TEMP_CRONTAB" 2>/dev/null || echo "# New crontab" > "$TEMP_CRONTAB"

# Check if the cleanup job already exists
if ! grep -q "cleanup.js" "$TEMP_CRONTAB"; then
  # Add the cleanup job to run every 6 hours
  echo "# Run SIMPERUM cleanup every 6 hours" >> "$TEMP_CRONTAB"
  echo "0 */6 * * * cd $SCRIPT_DIR && node --expose-gc cleanup.js >> $SCRIPT_DIR/cleanup.log 2>&1" >> "$TEMP_CRONTAB"
  
  # Install the new crontab
  crontab "$TEMP_CRONTAB"
  echo "Cron job installed successfully. Cleanup will run every 6 hours."
else
  echo "Cleanup cron job already exists."
fi

# Remove the temporary file
rm "$TEMP_CRONTAB"

echo "You can check your crontab with: crontab -l"
