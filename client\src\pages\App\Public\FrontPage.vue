<template>
  <v-container style="max-width: 100vw; padding: 0">
    <Header />

    <!-- <v-btn
      style="position: absolute; left: 50%; margin-left: -30px; top: 120px"
      @click="slideIndex = 0"
    >
      STOP
    </v-btn> -->
    <div
      style="
        background: rgba(255, 255, 255, 0.5);
        width: 100vw;
        height: calc(100vh - 176.5px);
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <v-carousel v-model="slideIndex" :cycle="doCycle" style="width: 1100px">
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/3XIJ54Huo_A"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/uDy52H9racI"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/0862kvTjYqk"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/9av7lYR319k"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/NFcUHpjKW3k"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/SBVJ2uJW4Ao"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
        <v-carousel-item>
          <iframe
            width="900"
            height="500"
            src="https://www.youtube.com/embed/X6Td1dv0r9Q"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.5)"
            allowfullscreen
          ></iframe>
        </v-carousel-item>
      </v-carousel>
    </div>
    <div
      style="
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        padding: 20px;
      "
    >
      REKAP DATA
    </div>
    <RekapAngka />
    <br />
    <br />
    <br />
    <Footer />
  </v-container>
</template>

<script>
import Header from '../Header.vue'
import Footer from '../Footer.vue'
import RekapAngka from '../../Dashboard/RekapAngka.vue'
import { mapActions } from 'vuex'
export default {
  components: {
    // eslint-disable-next-line vue/no-reserved-component-names
    Header,
    // eslint-disable-next-line vue/no-reserved-component-names
    Footer,
    RekapAngka,
  },
  data: () => ({
    doCycle: true,
    slideIndex: 0,
  }),
  computed: {},
  mounted() {
    if (localStorage.getItem('user')) {
      this.$router.push({ name: 'Home' })
    } else if (this.isMobile) {
      this.$router.push({ name: 'Login' })
    } else {
      setTimeout(() => {
        this.doCycle = false
      }, 12000)
    }
  },
  methods: {
    ...mapActions(['setMenu', 'setUser']),
    async StopCycle() {
      this.doCycle = false
    },
  },
}
</script>
<style lang="scss">
.v-carousel {
  .v-responsive__content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .v-carousel__controls {
    left: 100px !important;
    width: 82%;
  }
}
</style>
