<template>
  <div class="form-inline" style="padding: 10px">
    <div class="report-title" v-if="options">LAPORAN {{ options.rptname }}</div>
    <component
      v-for="com in components"
      :is="com.type"
      :key="com.id"
      :value.sync="params[com.id]"
      :dbref="com.dbref"
      :label="com.text"
    />
    <br />
    <v-btn
      color="primary"
      @click="Generate"
      :disabled="loading"
      v-tooltip="'Tampilkan Laporan'"
      v-show="!options || !options.action"
    >
      <div v-show="!loading">
        <v-icon left>mdi-eye</v-icon>
        <PERSON><PERSON><PERSON>an
      </div>
      <div v-show="loading">
        <v-icon left>access_time</v-icon> Mohon Tunggu ...
      </div>
    </v-btn>
    <!-- <v-btn
      color="green"
      @click="GenerateExcel"
      :disabled="loading"
      style="color: white; margin-left: 8px"
      v-tooltip="'Download Laporan Excel'"
      v-show="!options || !options.action"
    >
      <v-icon>mdi-microsoft-excel</v-icon>
    </v-btn> -->
    <v-btn
      color="green"
      @click="GenerateExcel"
      :disabled="loading"
      style="color: white; margin-top: 10px"
      v-tooltip="'Download Laporan'"
      v-show="options && options.action"
    >
      <div v-show="!loading">
        <v-icon left>mdi-file-download</v-icon>
        Download
      </div>
      <div v-show="loading">
        <v-icon left>access_time</v-icon> Mohon Tunggu ...
      </div>
    </v-btn>
    <br />
    <slot />
  </div>
</template>
<script>
import XSelect from '../../components/Forms/XSelect.vue'
export default {
  components: {
    XSelect,
  },
  data: () => ({
    params: {},
    components: [],
    loading: false,
  }),
  props: {
    options: Object,
    generatedUrl: String,
    reportParams: Object,
  },
  watch: {
    options() {
      this.getParams()
    },
  },
  methods: {
    async getParams() {
      let ret = await this.$api.post(`/report/GetParamsVue`, this.options)
      this.components = ret.data
    },
    async Generate() {
      this.loading = true
      this.$emit('generate')
      if (this.options.template) {
        let ret = await this.$api.post(
          `/report/template/` + this.options.template,
          {
            ...this.options,
            ...this.params,
            out: 'pdf',
          }
        )
        this.$emit('update:generatedUrl', this.$api.url + '/report' + ret.data)
        this.$emit('update:reportParams', {})
      } else {
        this.$emit('update:generatedUrl', '/report/Generic/html')
        this.$emit('update:reportParams', {
          ...this.options,
          ...this.params,
        })
      }
      // let ret = await this.$api.post(`/report/Generic/html`, {
      //   ...this.options,
      //   ...this.params,
      // })
      // this.$emit('update:generatedUrl', this.$api.url + '/report' + ret.data)
      this.loading = false
    },
    async GenerateExcel() {
      this.loading = true
      if (this.options.action) {
        let ret = await this.$api.post(this.options.action, {
          ...this.options,
          ...this.params,
        })
        this.$api.download(this.$api.url + '/report' + ret.data)
      } else if (this.options.template) {
        let ret = await this.$api.post(
          `/report/template/` + this.options.template,
          {
            ...this.options,
            ...this.params,
          }
        )
        this.$api.download(this.$api.url + '/report' + ret.data)
      } else if (this.options.custom) {
        let ret = await this.$api.post(
          `/report/custom/` + this.options.custom,
          {
            ...this.options,
            ...this.params,
          }
        )
        this.$api.download(this.$api.url + '/report' + ret.data)
      } else {
        let ret = await this.$api.post(`/report/Generic/xlsx`, {
          ...this.options,
          ...this.params,
        })
        this.$api.download(this.$api.url + '/report' + ret.data)
      }
      this.loading = false
    },
  },
}
</script>
<style lang="scss">
.report-title {
  margin: -12px 0 8px 0;
  font-family: 'Raleway';
  font-size: x-large;
}
</style>
