<template>
  <Page title="Varifikasi Hasil Laporan" :sidebar="true">
    <div style="background: white; overflow-x: hidden; overflow-y: auto">
      <div style="padding: 10px">
        <!-- <XSelect
          placeholder="Tujuan Disposisi"
          dbref="DOC.SelPegawai"
          :dbparams="{ nocache: true }"
          :value.sync="tujuanDisposisi"
          width="100%"
          @change="PopulateSideBar"
        /> -->
        <!-- <div class="tab-container">
          <input type="radio" name="tab" id="tab1" class="tab tab--1" />
          <label class="tab_label" for="tab1">Surat Masuk</label>

          <input type="radio" name="tab" id="tab2" class="tab tab--2" />
          <label class="tab_label" for="tab2">Surat Keluar</label>

          <div class="indicator"></div>
        </div> -->
      </div>
      <List
        :items.sync="suratList"
        @itemClick="SuratClicked"
        :selectOnLoad="true"
        style="height: calc(100vh - 220px); overflow-y: auto"
      >
        <template v-slot="{ row }">
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title style="display: flex">
                <span>{{ row.NoSurat }}</span>
              </v-list-item-title>
              <v-list-item-subtitle>
                {{ row.Perihal }}
              </v-list-item-subtitle>
              <v-list-item-subtitle>
                + {{ row.FullName }}
              </v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
        </template>
      </List>
    </div>
    <div v-if="form.LaporanUrl">
      <object
        :data="fileUrl"
        type="application/pdf"
        width="100%"
        style="height: calc(100vh - 110px)"
      >
        <p>
          File tidak ditemukan, silahkan upload ulang <br />
          {{ this.$api.url + this.form.LaporanUrl }}
        </p>
      </object>
      <!-- <iframe
        :src="this.$api.url + this.form.SuratUrl"
        width="100%"
        height="80%"
      >
      </iframe> -->
      <div
        style="
          padding: 25px;
          background: white;
          position: absolute;
          bottom: 0;
          right: 0;
        "
      >
        <v-btn @click="Cancel">BATAL</v-btn>
        <v-btn color="primary" style="margin-left: 10px" @click="Verify">
          TANDA TANGANI
        </v-btn>
      </div>
    </div>
  </Page>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data: () => ({
    suratList: [],
    pegawai: [],
    tujuanDisposisi: null,
    disposisi: [],
    form: {},
    jenisSurat: 'masuk',
    keyword: '',
  }),
  computed: {
    fileUrl() {
      return this.$api.url + this.form.LaporanUrl
    },
  },
  created() {
    const id = this.$route.query.id
    if (id) {
      // this.Populate(id)
      // this.PopulatePegawai(id)
      // this.setPageFocused(true)
    }
  },
  mounted() {
    this.PopulateSideBar()
  },
  methods: {
    ...mapActions(['setPageFocused']),
    SuratClicked(row) {
      this.form = row
      this.setPageFocused(true)
    },
    async PopulateSideBar() {
      let surats = await this.$api.call('DOC.SelSuratVerifikasi', {
        Status: 'submitted',
      })
      // console.log(surats.data)
      this.suratList = surats.data
      // console.log('suratList', this.suratList)
    },
    async Cancel() {
      this.form = {}
      this.setPageFocused(false)
    },
    async Verify() {
      // let ret = await this.$api.post('/api/docs/sign', {
      //   LaporanUrl: this.form.LaporanUrl.replace(/^\//, ''),
      // })
      // if (ret.success) {
      await this.$api.call('DOC.SavSuratVerify', {
        ...this.form,
      })
      await this.PopulateSideBar()
      this.form = {}
      this.setPageFocused(false)
      // }
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
.rowgrp {
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  font-size: small;
  padding: 5px;
  background: #f3f3f3;
}
.page-laporan-hasil-disposisi {
  .--input,
  textarea,
  .ui-upload {
    width: 100% !important;
  }
}

/* From Uiverse.io by zanina-yassine */
/* Remove this container when use*/
.component-title {
  width: 100%;
  position: absolute;
  z-index: 999;
  top: 30px;
  left: 0;
  padding: 0;
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: #888;
  text-align: center;
}

.tab-container {
  position: relative;

  display: flex;
  flex-direction: row;
  align-items: flex-start;

  padding: 2px;

  background-color: #dadadb;
  border-radius: 9px;
}

.indicator {
  content: '';
  width: 50%;
  height: 28px;
  background: #ffffff;
  position: absolute;
  top: 2px;
  left: 2px;
  z-index: 9;
  border: 0.5px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.12), 0px 3px 1px rgba(0, 0, 0, 0.04);
  border-radius: 7px;
  transition: all 0.2s ease-out;
}

.tab {
  width: 50%;
  height: 28px;
  position: absolute;
  z-index: 99;
  outline: none;
  opacity: 0;
}

.tab_label {
  width: 50%;
  height: 28px;

  position: relative;
  z-index: 999;

  display: flex;
  align-items: center;
  justify-content: center;

  border: 0;

  font-size: 0.75rem;
  opacity: 0.6;

  cursor: pointer;
}

.tab--1:checked ~ .indicator {
  left: 2px;
}

.tab--2:checked ~ .indicator {
  left: calc(50% - 2px);
}

.tab--3:checked ~ .indicator {
  left: calc(50% * 2 - 10px);
}
</style>
