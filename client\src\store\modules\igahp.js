import Vue from 'vue'

const initialState = () => ({
  igahpUser: {},
})
const state = initialState()

const getters = {
  getIgahpUser: (state) => {
    if (!state.user)
      return JSON.parse(sessionStorage.getItem('igahpUser') || '{}')
    else return state.user || {}
  },
}

const types = {
  SET_USER: 'igahp/SET_USER',
}

const actions = {
  setIgahpUser({ commit }, user) {
    sessionStorage.setItem('igahpUser', JSON.stringify(user))
    commit(types.SET_USER, user)
  },
}

const mutations = {
  [types.SET_USER](state, user) {
    // sessionStorage.setItem('user', JSON.stringify(user))
    Vue.set(state, 'user', user)
  },
}

export default {
  state,
  mutations,
  getters,
  actions,
  types,
}
