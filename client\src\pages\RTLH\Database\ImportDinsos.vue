<template>
  <Modal
    title="IMPORT DATA DARI DINSOS"
    :show.sync="xshow"
    :error="error"
    width="300px"
    @onSubmit="ImportERTLH"
    :submitText="submitText"
    cancelText="TUTUP"
  >
    <div style="width:400px;" class="form-inline">
      <XSelect
        label="Kabupaten"
        dbref="PRM.SelPBDTCity"
        :dbparams="{ nocache: true }"
        :value.sync="kabupaten"
        :valueAsObject="true"
        width="190px"
      />
      <XSelect
        label="Kecamatan"
        dbref="Arch.SelArea"
        :dbparams="{ ParentAreaID: kabupaten.val }"
        :value.sync="kecamatan"
        :valueAsObject="true"
        width="190px"
      />
      <XSelect
        label="Kelurahan"
        dbref="Arch.SelArea"
        :dbparams="{ ParentAreaID: kecamatan.AreaID }"
        :value.sync="kelurahan"
        :valueAsObject="true"
        width="190px"
      />
      <div v-show="loading">Importing ...</div>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    failedRows: null,
    kabupaten: { val: null, txt: null },
    kecamatan: { val: null, txt: null },
    kelurahan: { val: null, txt: null },
    error: '',
    loading: false,
    // submitText: 'IMPORT',
  }),
  props: {
    show: Boolean,
  },
  computed: {
    submitText() {
      return this.loading ? '' : 'IMPORT'
    },
  },
  watch: {
    show(val) {
      this.xshow = val
      this.loading = false
    },
    xshow(val) {
      this.$emit('update:show', val)
      if (!val) this.error = ''
    },
  },
  methods: {
    async ImportERTLH() {
      if (!this.kelurahan.KodeDagri) {
        this.loading = true
        // IMPORT FROM DINSOS
        let ret = await this.$api.post(
          '/api/dinsos/import-kab/' + this.kabupaten.txt
        )
        if (ret.success) {
          this.loading = false
          this.xshow = false
        }
      } else {
        this.loading = true
        // IMPORT FROM DINSOS
        let ret = await this.$api.post(
          '/api/dinsos/import/' + this.kelurahan.AreaID
        )
        if (ret.success) {
          this.loading = false
          this.xshow = false
        }
      }
    },
  },
}
</script>
<style lang="scss">
.modal-import-data-dari-e-rtlh {
  .v-dialog--content {
    overflow: hidden;
  }
}
</style>
