<template>
  <Page title="Data Pengguna">
    <template v-slot:toolbar>
      <v-btn small @click="showRolesPage = true">+ Role</v-btn>
    </template>
    <Grid
      :datagrid.sync="users"
      dbref="Arch.User"
      style="height: calc(100vh - 120px)"
      class="dense"
      :columns="[
        {
          name: 'Username',
          value: 'Username',
          width: '120px',
          editable: {
            com: 'XInput',
          },
          filter: {
            type: 'search',
          },
        },
        {
          name: 'Password',
          value: 'Password',
          width: '120px',
          editable: {
            com: 'XInput',
            type: 'password',
          },
        },
        {
          name: '<PERSON><PERSON>',
          value: 'FullName',
          width: '180px',
          editable: {
            com: 'XInput',
          },
        },
        {
          name: 'Aktif ?',
          value: 'IsActive',
          width: '70px',
          editable: {
            com: 'Checkbox',
          },
        },
        {
          name: 'Role',
          class: 'plain',
          value: 'RolePositionName',
          editable: {
            com: 'Select',
            value: 'RolePositionID',
            text: 'RolePositionName',
            dbref: 'Arch.SelRolePosition',
          },
        },
        {
          name: 'Area',
          class: 'center',
          width: '50px',
          value: 'AreaAccess',
        },
      ]"
    >
      <template v-slot:row-IsActive="{ row }">
        <div center>
          <v-icon>
            {{
              row.IsActive ? 'mdi-checkbox-marked-outline' : 'mdi-crop-square'
            }}
          </v-icon>
        </div>
      </template>
      <template v-slot:row-Password="{ row }">
        <div center>*******</div>
      </template>
      <template v-slot:row-RolePositionName="{ row }">
        <v-btn
          text
          small
          color="primary"
          @click="ShowRoleAccess(row.RolePositionID)"
        >
          {{ row.RolePositionName }}
        </v-btn>
      </template>
      <template v-slot:row-AreaAccess="{ row }">
        <div center>
          <v-icon @click="ShowAreaAccess(row.UserID)">
            mdi-map-marker-path
          </v-icon>
        </div>
      </template>
    </Grid>
    <Modal
      id="modal-role-access"
      :show.sync="roleAccess"
      title="ROLE ACCESS"
      width="350px"
      @onSubmit="SubmitRoleAccess"
    >
      <RoleAccessPage :roleId="roleId" :page-data.sync="roleAccessData" />
    </Modal>
    <Modal
      id="modal-roles"
      :show.sync="showRolesPage"
      title="ROLES"
      @onSubmit="SubmitRoles"
    >
      <RolesPage :page-data.sync="rolesData" />
    </Modal>
    <Modal
      id="modal-roles"
      :show.sync="showAreaAccess"
      title="AREA AKSES"
      @onSubmit="SubmitAreaAccess"
    >
      <AreaAccessPage :userId="userId" :page-data.sync="areaAccessData" />
    </Modal>
  </Page>
</template>
<script>
import RoleAccessPage from './RoleAccess.vue'
import RolesPage from './Roles.vue'
import AreaAccessPage from './AreaAccess.vue'

export default {
  components: {
    RoleAccessPage,
    RolesPage,
    AreaAccessPage,
  },
  data: () => ({
    roleAccess: false,
    showRolesPage: false,
    showAreaAccess: false,
    roleAccessData: [],
    rolesData: [],
    areaAccessData: [],
    roleId: null,
    userId: null,
    users: null,
    forms: {},
  }),
  async mounted() {},
  methods: {
    ShowRoleAccess(roleId) {
      this.roleAccess = true
      this.roleId = roleId
    },
    ShowAreaAccess(userId) {
      this.showAreaAccess = true
      this.userId = userId
    },
    async SubmitRoles() {},
    async SubmitRoleAccess() {
      let ret = this.$api.call('Arch.SavRoleAccess', {
        RolePositionID: this.roleId,
        XmlRoleAccess: this.roleAccessData,
      })
      if (ret.success) this.roleAccess = false
    },
    async SubmitAreaAccess() {
      let ret = await this.$api.call('Arch.SavUserArea', {
        UserID: this.userId,
        Remarks: this.areaAccessData
          .filter((a) => a.AllowAccess)
          .map((a) => a.AreaID)
          .join('|'),
      })

      if (ret.success) this.showAreaAccess = false
    },
  },
}
</script>
<style lang="scss">
#modal-role-access {
  .ui-table {
    height: 500px !important;
  }
}
</style>
