<template>
  <Page title="Database RTLH">
    <template v-slot:toolbar>
      <!-- <v-menu offset-y>
        <template v-slot:activator="{ on }">
          <v-btn small color="primary" v-on="on">IMPORT</v-btn>
        </template>
        <v-list dense style="width: 250px">
          <v-list-item @click="OpenImportExcel">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-excel</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Dari Excel</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="OpenImportERTLH">
            <v-list-item-icon>
              <v-icon>mdi-home-import-outline</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Dari e-RTLH</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="OpenImportByNIK">
            <v-list-item-icon>
              <v-icon>mdi-home-import-outline</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Dari NIK DTKS</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-icon @click="showCariNIK = true">mdi-account-search</v-icon>
      <v-icon @click="showMap = !showMap">mdi-map-search</v-icon>
      <v-icon @click="BeforePrint()">print</v-icon> -->
    </template>
    <Grid
      id="table-database-rtlh"
      :datagrid.sync="backlog"
      dbref="PRM.PBDT"
      :dbparams.sync="filters"
      class="dense"
      :preHead="true"
      :disabled="true"
      :height="'calc(100vh - 120px)'"
      v-show="!showMap"
      :doRebind="pbdtRebind"
      :columns="[
        {
          name: '',
          value: 'VerStatsID',
          class: 'plain center',
          width: '50px',
          print: {
            name: 'Status',
          },
        },
        {
          name: 'NIK',
          value: 'NIK',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: 'Nama',
          value: 'KRT_Nama',
          width: '200px',
          class: 'plain fix-width',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: 'IDBDT',
          value: 'IDBDT',
          hide: true,
          filter: {
            type: 'search',
            value: 'IDBDT',
          },
        },
        {
          name: 'No. KK',
          value: 'NoKK',
          hide: true,
          filter: {
            type: 'search',
            value: 'NoKK',
          },
        },
        {
          name: 'Alamat',
          value: 'Alamat',
          width: '250px',
          class: 'fix-width',
          filter: {
            type: 'search',
            value: 'Alamat',
          },
        },
        {
          name: '',
          value: 'GeoLoc',
          class: 'plain center',
          print: {
            name: 'Koordinat',
          },
        },
        {
          name: 'Umur',
          value: 'Umur',
          hide: true,
        },
        {
          name: 'Jns Kel',
          value: 'JenisKelamin',
          hide: true,
        },
        {
          name: 'Pendidikan',
          value: 'Pendidikan',
          hide: true,
        },
        {
          name: 'Pekerjaan',
          value: 'Pekerjaan',
          hide: true,
        },
        {
          name: 'Penghasilan',
          value: 'Penghasilan',
          hide: true,
        },
        {
          name: 'Jml KK',
          value: 'JmlKK',
          hide: true,
        },
        {
          name: 'Jml Penghuni',
          value: 'JmlPenghuni',
          hide: true,
        },
        {
          name: 'Status Tanah',
          value: 'StatusTanah',
          hide: true,
        },
        {
          name: 'Status Rumah',
          value: 'StatusRumah',
          hide: true,
        },
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          filter: {
            type: 'select',
            value: 'KabupatenID',
            text: 'Kabupaten',
            dbref: 'Arch.SelArea',
            dbparams: { ParentAreaID: 33 },
          },
        },
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          filter: {
            type: 'select',
            value: 'KecamatanID',
            text: 'Kecamatan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KabupatenID }),
          },
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          filter: {
            type: 'select',
            value: 'KelurahanID',
            text: 'Kelurahan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KecamatanID }),
          },
        },
        {
          name: 'Atap',
          value: 'Atap',
          hide: true,
        },
        {
          name: 'Lantai',
          value: 'Lantai',
          hide: true,
        },
        {
          name: 'Dinding',
          value: 'Dinding',
          hide: true,
        },
        {
          name: 'Jendela',
          value: 'AdaJendela',
          hide: true,
        },
        {
          name: 'Ventilasi',
          value: 'AdaVentilasi',
          hide: true,
        },
        {
          name: 'Sumber Air',
          value: 'SumberAir',
          hide: true,
        },
        {
          name: 'Kamar Mandi',
          value: 'KamarMandi',
          hide: true,
        },
        {
          name: 'Penerangan',
          value: 'Penerangan',
          hide: true,
        },
        {
          name: 'Jrk Septik Tnk',
          value: 'JarakSepticTank',
          hide: true,
        },
        {
          name: 'Prior',
          value: 'Prioritas',
          class: 'center',
        },
        {
          name: 'DT',
          value: 'NamaData',
          filter: {
            type: 'select',
            value: 'TipeData',
            text: 'NamaTipeData',
            dbref: 'PRM.SelTipeData',
          },
        },
        {
          name: 'Skor',
          value: 'ScoreTag',
          hide: true,
        },
        {
          name: 'Intervensi',
          value: 'Remarks',
          filter: {
            type: 'select',
            value: 'Sumber',
            text: 'SumberDana',
            dbref: 'PRM.SelSumber',
          },
        },
      ]"
      :doPrint="doPrint"
      @after-bind="AfterGridBind"
    >
      <template v-slot:pre-head="{ allfilters, openColumnSetting }">
        <Panel dbref="PRM.SelDescPBDT" :dbparams="allfilters">
          <template v-slot="{ first }">
            <div style="display: flex" class="pre-head">
              <div style="padding: 8px 12px; flex: 2; font-size: 14px">
                <XInput
                  rightIcon="search"
                  width="230px"
                  placeholder="Cari NIK/Nama .."
                  :value.sync="keyword"
                />
                <!-- <v-btn @click="showFilters = true">
                  <v-icon left>mdi-table-search</v-icon>
                  Pencarian
                </v-btn> -->
              </div>
              <div class="table-desc" style="">
                <v-btn text small color="gray" @click="openColumnSetting">
                  <v-icon left>mdi-cog</v-icon>
                  ATUR KOLOM
                </v-btn>
                <div style="background-color: #95a5a6">
                  Total: {{ first.Total | format }}
                </div>
                <div style="background-color: #2ecc71">
                  Intervensi {{ first.Intervensi | format }}
                </div>
                <div style="background-color: #ecf0f1; color: gray">
                  Sisa: {{ first.Sisa | format }}
                </div>
              </div>
            </div>
          </template>
        </Panel>
      </template>
      <template v-slot:row-NIK="{ row }">
        <nik-block :nik="row.NIK" />
      </template>
      <template v-slot:row-KRT_Nama="{ row }">
        <v-btn text small color="primary" @click.stop="OpenDetail(row.IDBDT)">
          {{ row.KRT_Nama }}
        </v-btn>
      </template>
      <template v-slot:row-Prioritas="{ row }">
        <v-icon
          :color="priorcolors[row.Prioritas]"
          v-tooltip="`Prioritas ${row.Prioritas}`"
        >
          mdi-numeric-{{ row.Prioritas }}-circle
        </v-icon>
      </template>
      <template v-slot:row-VerStatsID="{ row }">
        <v-icon
          v-if="row.VerStatsID >= 6"
          color="primary"
          v-tooltip="'Sudah Terverifikasi'"
        >
          mdi-account-check
        </v-icon>
        <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
          mdi-account-question-outline
        </v-icon>
      </template>
      <template v-slot:row-NamaData="{ row }">
        <span>{{ row.NamaData }}</span>
        <span
          v-if="row.DataTag"
          style="
            font-size: 10px;
            margin-left: 5px;
            background: lightblue;
            padding: 3px;
            border-radius: 3px;
          "
        >
          {{ row.DataTag }}
        </span>
      </template>
      <template v-slot:row-GeoLoc="{ row }">
        <v-icon
          v-tooltip="'Lihat Lokasi pada Peta'"
          @click.stop="OpenMap(row.NoUrutRT)"
        >
          mdi-map-marker-radius
        </v-icon>
      </template>
    </Grid>
    <DBMap
      v-show="showMap"
      dbref="PRM.SelPBDTMap"
      :dbparams="filters"
      :noRef="selectedRef"
      :disabled="true"
    />
    <ValidasiDetail
      :show.sync="showDetailModal"
      :noRef="selectedRef"
      :disabled="true"
    />
    <ImportExcel :show.sync="showImportExcel" />
    <ImportERTLH :show.sync="showImportERTLH" />
    <ImportDinsos :show.sync="showImportDinsos" />
    <Modal
      title="IMPORT BERDASARKAN NIK"
      :show="showImportNIK"
      @onCancel="showImportNIK = false"
      @onSubmit="ImportMultipleNIK"
      :submitText="importNIKText"
    >
      <TextArea
        label="Masukkan beberapa NIK:"
        width="400px"
        style="height: 230px"
        :value.sync="multipleNIK"
      />
      <template v-slot:left-action>
        <div style="font-size: small">{{ importStatus }}</div>
      </template>
    </Modal>
    <Modal
      title="CARI BERDASARKAN NIK"
      :show="showCariNIK"
      @onCancel="showCariNIK = false"
      @onSubmit="CariMultipleNIK"
      submitText="CARI & DOWNLOAD"
    >
      <TextArea
        label="Masukkan beberapa NIK:"
        width="400px"
        style="height: 230px"
        :value.sync="multipleNIK"
      />
    </Modal>
    <Modal
      title="FILTERS"
      :show="showFilters"
      @onCancel="showFilters = false"
      @onSubmit="ApplyFilters"
      submitText="TERAPKAN"
    >
      <AdvancedFilters :filters="filters" />
    </Modal>
  </Page>
</template>
<script>
import ReportViewer from '../../../components/ReportViewer.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import AdvancedFilters from './AdvancedFilters.vue'
import DBMap from './Map.vue'
import ImportExcel from './ImportExcel.vue'
import ImportERTLH from './ImportERTLH.vue'
import ImportDinsos from './ImportDinsos.vue'

export default {
  components: {
    ValidasiDetail,
    ReportViewer,
    DBMap,
    AdvancedFilters,
    ImportExcel,
    ImportERTLH,
    ImportDinsos,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    showDetailModal: false,
    showCariNIK: false,
    showImportNIK: false,
    importNIKText: 'IMPORT',
    showImportExcel: false,
    showImportERTLH: false,
    showImportDinsos: false,
    importStatus: '',
    showMap: false,
    filters: {},
    gridFilters: {},
    showFilters: false,
    selectedRef: null,
    lat: null,
    lon: null,
    backlog: [],
    reportUrl: null,
    doPrint: 0,
    reportOptions: {},
    multipleNIK: null,
    keyword: '',
    pbdtRebind: 1,
  }),
  async mounted() {},
  watch: {
    keyword(val) {
      this.filters.Keyword = val
      this.pbdtRebind++
    },
  },
  methods: {
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenMap(noRef) {
      this.selectedRef = noRef
      this.showMap = true
    },
    OpenImportExcel() {
      this.showImportExcel = true
    },
    OpenImportERTLH() {
      this.showImportERTLH = true
    },
    OpenImportDinsos() {
      this.showImportDinsos = true
    },
    OpenImportByNIK() {
      this.showImportNIK = true
    },
    AfterGridBind(filters) {
      this.gridFilters = filters
    },
    BeforePrint() {
      if (!this.gridFilters.Kabupaten) {
        this.$api.notify('Harap difilter berdasar Kabupaten.', 'error')
      } else {
        this.doPrint++
      }
    },
    Print(headers, data) {
      this.reportOptions = {
        headers: headers,
        data: data,
      }
      this.showReport = true
    },
    async ImportMultipleNIK() {
      this.importNIKText = ''
      this.importStatus = 'preparing..'
      let niks = this.multipleNIK
        .split('\n')
        .map((d) => {
          return d.replace(/[^\d]+/gm, '').trim()
        })
        .filter((d) => d)
        .join(',')
      let d = await this.$api.call('PRM.RptSearchByNIK', {
        NIKS: niks,
      })
      let i = 1
      for (let p of d.data) {
        if (p.SumberName == 'TIDAK TERDAFTAR') {
          await this.importDinsosByNIK(p.NIK)
        } else if (p.TipeData.match(/MANUAL/)) {
          await this.importDinsosByNIK(p.NIK, true)
        }
        this.importStatus = `importing ${i}/${d.data.length}`
        i++
      }
      this.importNIKText = 'IMPORT'
      this.importStatus = ''
    },
    async importDinsosByNIK(nik, isUpdate = false) {
      let d = await this.$api.post(this.$api.url + '/api/dinsos/get/' + nik)
      let forms = {}
      if (d.success && d.data.ALAMAT && d.data.kddesa) {
        if (forms.TipeData > 200 && forms.TipeData < 300) {
          forms.NewTipeData -= new Date().getYear()
        }

        let alamat =
          d.data.ALAMAT.trim() + ' RT ' + d.data.RT + ' RW ' + d.data.RW
        forms.NIK = nik
        forms.Nama = d.data.Nama.trim()
        forms.Alamat = alamat
        forms.IDBDT = (d.data.IDBDT || d.data.id_dtks).toLowerCase()
        forms.NIKBDT = nik
        forms.NamaBDT = d.data.Nama.trim()
        forms.NewTipeData = new Date().getYear()
        forms.KodeWilayah = '33' + d.data.kdkab + d.data.kdkec + d.data.kddesa
        if (isUpdate) {
          await this.$api.call('PRM.UpdTipeData', forms, {
            silent: true,
          })
        } else {
          await this.$api.call('PRM.SavPBDTDetail', forms, {
            silent: true,
          })
        }
        await this.sleep(1000)
      }
    },
    sleep(ms) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve()
        }, ms)
      })
    },
    async CariMultipleNIK() {
      let niks = this.multipleNIK
        .split('\n')
        .map((d) => {
          return d.replace(/[^\d]+/gm, '').trim()
        })
        .filter((d) => d)
        .join(',')
      let ret = await this.$api.post(
        '/report/generate/xlsx?sp=PRM_RptSearchByNIK&rptname=CariNIK',
        {
          NIKS: niks,
        }
      )
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
    async ApplyFilters() {},
  },
}
</script>
<style lang="scss">
.page-database-rtlh {
  .page-content {
    overflow: auto;
  }
  .table-desc {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;

    div {
      padding: 3px 5px;
      margin-left: 3px;
      color: white;
    }
  }
}
</style>
