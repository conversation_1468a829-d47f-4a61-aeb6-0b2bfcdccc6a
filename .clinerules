# UI rules

when user ask you to create a page then use the <Page /> component

when user ask you to create modal or dialog, please use <Modal /> component
for template usage components/usage/Modal.vue

use <XInput label="" /> component instead of default Input component
use <XSelect label="" :items="[{}]" /> component instead of default <select /> component

# Database rules

when trying to looking for database, use mysql database mcp and use server/.env for the credential
please try connecting if the connection broken or failed

# Stored Procedure rules

when creating stored procedure, please refer to server/storedproc.md


