<template>
  <Page title="Alokasi RTLH">
    <div class="padding">
      <div style="display:flex;">
        <!-- <XSelect
          :value.sync="jenisKuota"
          :items="[
            { val: 'kuota_kabupaten', txt: 'Kuota Kabupaten' },
            { val: 'kuota_dewan', txt: 'Kuota Dewan' },
          ]"
          style="margin-right:10px;"
        /> -->
      </div>
      <Desa v-if="jenisKuota == 'kuota_desa'" />
      <Kabupaten v-else-if="jenisKuota == 'kuota_kabupaten'" />
      <Dewan v-else-if="jenisKuota == 'kuota_dewan'" />
    </div>
  </Page>
</template>
<script>
import Desa from './Desa.vue'
import Kabupaten from './Kabupaten.vue'
import Dewan from './Dewan.vue'

export default {
  components: {
    Desa,
    Kabupaten,
    Dewan,
  },
  data: () => ({
    proposal: {
      InputName: new Date().getFullYear(),
    },
    jenis<PERSON>uota: 'kuota_kabupaten',
    hasAllocation: false,
  }),
  computed: {
    params() {
      let { Kabupaten, Sumber } = this.forms
      let { InputName } = this.proposal
      return { Kabupaten, Sumber, Tahun: InputName }
    },
  },
  methods: {
    async Save() {
      await this.$api.call('PRM_SavAlokasi', {
        ...this.forms,
        Tahun: this.proposal.InputName,
      })
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
  },
}
</script>
<style lang="scss"></style>
