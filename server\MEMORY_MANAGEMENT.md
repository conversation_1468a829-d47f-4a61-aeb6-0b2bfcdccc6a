# Memory Management Guide

This document provides guidance on managing memory usage and preventing memory leaks in the SIMPERUM backend application.

## Common Memory Leak Issues

The application may experience memory leaks due to:

1. **Database Connection Pooling**: Connections not properly released
2. **File Upload Processing**: Temporary files not cleaned up
3. **Caching**: Unbounded cache growth
4. **Stream Handling**: Unclosed streams
5. **Event Listeners**: Accumulated event listeners

## Running with Garbage Collection Enabled

To enable manual garbage collection, start the server with the `--expose-gc` flag:

```bash
# Using npm script
npm run start:gc

# Or directly
node --expose-gc app.js

# On Windows
start-with-gc.bat

# On Linux/Mac
./start-with-gc.sh
```

## Periodic Cleanup

A cleanup script has been implemented to periodically:

1. Clean up temporary files
2. Monitor memory usage
3. Force garbage collection when needed

Run the cleanup script:

```bash
npm run cleanup
```

## Memory Monitoring

To monitor memory usage in real-time:

```bash
npm run monitor
```

## Best Practices

1. **Database Connections**:
   - Always release connections after use
   - Use connection pooling with appropriate limits

2. **File Handling**:
   - Always close file streams
   - Clean up temporary files after processing
   - Use try/catch/finally blocks to ensure cleanup

3. **Caching**:
   - Implement cache size limits
   - Set expiration policies
   - Periodically clean up expired cache items

4. **Event Listeners**:
   - Remove event listeners when no longer needed
   - Avoid anonymous functions as event listeners

5. **Memory Leaks Detection**:
   - Use the `/api/health` endpoint to check server health
   - Monitor memory usage with the built-in monitoring tools

## Troubleshooting

If you encounter "Out of Memory" errors:

1. Check the server logs for memory usage patterns
2. Run the cleanup script to free up resources
3. Restart the server with garbage collection enabled
4. Consider increasing the Node.js memory limit:
   ```
   node --max-old-space-size=4096 --expose-gc app.js
   ```

## Docker Configuration

When running in Docker, update the container configuration to allocate sufficient memory:

```yaml
services:
  backend:
    image: indrawan/perum:server-12
    # ... other settings
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
```

## Scheduled Maintenance

Set up a cron job to run the cleanup script periodically:

```
# Run cleanup every 6 hours
0 */6 * * * cd /path/to/app && npm run cleanup >> /var/log/simperum-cleanup.log 2>&1
```
