const axios = require("axios");
var express = require('express')
var router = express.Router()
const FormData = require('form-data');
const {authMiddleware} = require('../auth')

let gistaruData = null
const mapServers = [
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A1_PERKOTAAN_PURWOKERTO/MapServer/0",
    "type": "MapServer", "Kabupaten": "BANYUMAS", "Kecamatan": ["PURWOKERTO"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A2_TULIS/MapServer/0",
    "type": "MapServer", "Kabupaten": "BATANG", "Kecamatan": ["TULIS"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A3_SUKOHARJO/MapServer/0",
    "type": "MapServer", "Kabupaten": "SU<PERSON><PERSON><PERSON><PERSON><PERSON>", "Kecamatan": ["SUKOHARJ<PERSON>"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A4_KARTASURA/MapServer/0",
    "type": "MapServer", "Kabupaten": "SUKOHARJO", "Kecamatan": ["KARTASURA"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A5_PERKOTAAN_GROGOL/MapServer/0",
    "type": "MapServer", "Kabupaten": "SUKOHARJO", "Kecamatan": ["GROGOL"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A6_PERKOTAAN_CILACAP/MapServer/0",
    "type": "MapServer", "Kabupaten": "CILACAP"
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A7_KEDUNGWUNI/MapServer/0",
    "type": "MapServer", "Kabupaten": "PEKALONGAN", "Kecamatan": ["KEDUNGWUNI"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A8_PERKOTAAN_PURBALINGGA/MapServer/0",
    "type": "MapServer", "Kabupaten": "PURBALINGGA"
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33A9_KECAMATAN_JEPARA/MapServer/0",
    "type": "MapServer", "Kabupaten": "JEPARA", "Kecamatan": ["JEPARA"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B1_KECAMATAN_SAWIT/MapServer/0",
    "type": "MapServer", "Kabupaten": "BOYOLALI", "Kecamatan": ["SAWIT"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B2_PERKOTAAN_SRAGEN/MapServer/0",
    "type": "MapServer", "Kabupaten": "SRAGEN", "Kecamatan": ["SRAGEN"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B3_BWP_PK_I_II_III_DAN_IV_KOTA_SALATIGA/MapServer/0",
    "type": "MapServer", "Kabupaten": "KOTA SALATIGA"
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B4_PURWOREJO_KUTOARJO/MapServer/0",
    "type": "MapServer", "Kabupaten": "PURWOREJO"
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B5_WILAYAH_PERENCANAAN_KECAMATAN_MOJOSONGO/MapServer/0",
    "type": "MapServer", "Kabupaten": "BOYOLALI", "Kecamatan": ["MOJOSONGO"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B6_WP_KECAMATAN_BANYUDONO/MapServer/0",
    "type": "MapServer", "Kabupaten": "BOYOLALI", "Kecamatan": ["BANYUDONO"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B7_KAWASAN_PERKOTAAN_WONOGIRI/MapServer/0",
    "type": "MapServer", "Kabupaten": "WONOGIRI", "Kecamatan": ["WONOGIRI"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B8_KAWASAN_PERKOTAAN_SOKARAJA/MapServer/0",
    "type": "MapServer", "Kabupaten": "BANYUMAS", "Kecamatan": ["SOKARAJA"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33B9_KAWASAN_PERKOTAAN_BANYUMAS/MapServer/0",
    "type": "MapServer", "Kabupaten": "BANYUMAS", "Kecamatan": ["BANYUMAS"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C1_WILAYAH_PERENCANAAN_LIMPUNG_2023/MapServer/0",
    "type": "MapServer", "Kabupaten": "BATANG", "Kecamatan": ["LIMPUNG"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C2_WILAYAH_PERENCANAAN_GRINGSING/MapServer/0",
    "type": "MapServer", "Kabupaten": "BATANG", "Kecamatan": ["GRINGSING"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C3_KAWASAN_PERKOTAAN_SIDAREJA_/MapServer/0",
    "type": "MapServer", "Kabupaten": "CILACAP", "Kecamatan": ["SIDAREJA"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C4_KAWASAN_KOTA_INDUSTRI_GONDANG_SAMBUNGMACAN/MapServer/0",
    "type": "MapServer", "Kabupaten": "SRAGEN", "Kecamatan": ["SAMBUNGMACAN"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C5_KECAMATAN_LOSARI/MapServer/0",
    "type": "MapServer", "Kabupaten": "BREBES", "Kecamatan": ["LOSARI"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C6_KOTA_TEGAL",
    "type": "MapServer", "Kabupaten": "KOTA TEGAL"
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C7_KECAMATAN_TANJUNG",
    "type": "MapServer", "Kabupaten": "BREBES", "Kecamatan": ["TANJUNG"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C8_KAWASAN_PERKOTAAN_GEMOLONG",
    "type": "MapServer", "Kabupaten": "SRAGEN", "Kecamatan": ["GEMOLONG"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33C9_KECAMATAN_BULAKAMBA",
    "type": "MapServer", "Kabupaten": "BREBES", "Kecamatan": ["BULAKAMBA"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33D1_KAWASAN_PERKOTAAN_BOBOTSARI",
    "type": "MapServer", "Kabupaten": "PURBALINGGA", "Kecamatan": ["BOBOTSARI"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33D2_WP_KECAMATAN_CEPOGO",
    "type": "MapServer", "Kabupaten": "BOYOLALI", "Kecamatan": ["CEPOGO"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33D3_KAWASAN_KOTA_PERBATASAN_SEKITAR_BIY",
    "type": "MapServer", "Kabupaten": "PURWOREJO", "Kecamatan": ["BAGELEN", "PURWODADI"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33D6_WP_GUNUNG_SEWU_SEGMEN_WONOGIRI",
    "type": "MapServer", "Kabupaten": "WONOGIRI", "Kecamatan": ["PRACIMANTORO"]
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33E3_KAWASAN_PERKOTAAN_DEMAK",
    "type": "MapServer", "Kabupaten": "DEMAK"
  },
  {
    "name": "056_RDTR_PROVINSI_JAWA_TENGAH/_RDTR_33E4_KECAMATAN_NGUTER_DAN_KECAMATAN_BENDOSARI",
    "type": "MapServer", "Kabupaten": "SUKOHARJO", "Kecamatan": ["NGUTER","BENDOSARI"]
  },
  // RTRW
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/0",
    "type": "MapServer", "Kabupaten": "CILACAP"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/1",
    "type": "MapServer", "Kabupaten": "BANYUMAS"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/2",
    "type": "MapServer", "Kabupaten": "PURBALINGGA"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/3",
    "type": "MapServer", "Kabupaten": "BANJARNEGARA"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/4",
    "type": "MapServer", "Kabupaten": "KEBUMEN"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/5",
    "type": "MapServer", "Kabupaten": "PURWOREJO"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/6",
    "type": "MapServer", "Kabupaten": "WONOSOBO"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/7",
    "type": "MapServer", "Kabupaten": "MAGELANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/8",
    "type": "MapServer", "Kabupaten": "BOYOLALI"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/9",
    "type": "MapServer", "Kabupaten": "KLATEN"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/10",
    "type": "MapServer", "Kabupaten": "SUKOHARJO"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/11",
    "type": "MapServer", "Kabupaten": "WONOGIRI"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/12",
    "type": "MapServer", "Kabupaten": "KARANGANYAR"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/13",
    "type": "MapServer", "Kabupaten": "SRAGEN"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/14",
    "type": "MapServer", "Kabupaten": "GROBOGAN"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/15",
    "type": "MapServer", "Kabupaten": "BLORA"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/16",
    "type": "MapServer", "Kabupaten": "REMBANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/17",
    "type": "MapServer", "Kabupaten": "PATI"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/18",
    "type": "MapServer", "Kabupaten": "KUDUS"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/19",
    "type": "MapServer", "Kabupaten": "JEPARA"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/20",
    "type": "MapServer", "Kabupaten": "DEMAK"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/21",
    "type": "MapServer", "Kabupaten": "SEMARANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/22",
    "type": "MapServer", "Kabupaten": "TEMANGGUNG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/23",
    "type": "MapServer", "Kabupaten": "KENDAL"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/24",
    "type": "MapServer", "Kabupaten": "BATANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/25",
    "type": "MapServer", "Kabupaten": "PEKALONGAN"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/26",
    "type": "MapServer", "Kabupaten": "PEMALANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/27",
    "type": "MapServer", "Kabupaten": "TEGAL"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/28",
    "type": "MapServer", "Kabupaten": "BREBES"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/29",
    "type": "MapServer", "Kabupaten": "KOTA MAGELANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/30",
    "type": "MapServer", "Kabupaten": "KOTA SURAKARTA"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/31",
    "type": "MapServer", "Kabupaten": "KOTA SALATIGA"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/32",
    "type": "MapServer", "Kabupaten": "KOTA SEMARANG"
  },
  {
    "name": "022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/33",
    "type": "MapServer", "Kabupaten": "KOTA PEKALONGAN"
  },
]

const refreshToken = async () => {
  let form = new FormData();
  form.append('username', 's1mp312Um.G15t412u');
  form.append('password', '49!@6baefb2@03b14c4a345034@b4bed53d8c55ca7995e0768d!727338bef25e!@d9355');
  form.append('ip', '');
  form.append('client', 'referer');
  form.append('referer', 'https://gistaru.atrbpn.go.id/');
  form.append('expiration', '20160');
  form.append('f', 'json');
    
  let d = await axios.post('https://gistaru.atrbpn.go.id/portal/sharing/rest/generateToken', form)
  return d
}

const getGistaru = async (lat, lon, kabupaten, kecamatan) => {
  // if (!gistaruData || gistaruData.expires < new Date().getTime()) {
  //   let d = await refreshToken()
  //   gistaruData = d.data
  //   console.log('gistaru: ', gistaruData)
  // }
  gistaruData = {
    token: 'c57hqOTTatOMQNusgRjnmVlnfS6r9csRcEcd4MuTlj7eXc7uQH_32I9dwVnCm449mo0ysBlUS9lUvdeNYdzDWDqSayPCLzKq6UMuH4LIE5Qu6W6KcW6SEM8B6rsnInjUms1Bp8IxYVaIWekVuyK-YwzXM5ZYdBnFxDGI3KSLxdQ.',
    expires: 1729653190311,
    ssl: true
  }

  let mapObj = mapServers.filter(m => m.Kabupaten == kabupaten)
  // console.log(kabupaten, kecamatan, mapObj)
  const kabObj = mapObj.length > 1 ? mapObj[mapObj.length - 1] : null
  if (mapObj?.length > 1) {
    if(kecamatan) {
      let mx = mapObj.filter(m => (m.Kecamatan||[]).includes(kecamatan))
      if (mx.length) {
        mapObj = mx[0]
      } else {
        mapObj = mapObj.filter(m => !m.Kecamatan)
      }
    } else {
      mapObj = mapObj.filter(m => !m.Kecamatan)
    }
  }
  if (!mapObj) mapObj = kabObj
  if (mapObj.length) mapObj = mapObj[0]
  console.log(kabupaten, kecamatan, mapObj)
  // https://gistaru.atrbpn.go.id/proxy_jateng/run.ashx?https://gistaru.atrbpn.go.id/arcgis/rest/services/022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/?f=json
  // https://gistaru.atrbpn.go.id/proxy_jateng/run.ashx?https://gistaru.atrbpn.go.id/arcgis/rest/services/022_RTR_KABUPATEN_KOTA_PROVINSI_JAWA_TENGAH/_3300_JAWA_TENGAH_PR_PERDA/MapServer/1/query?geometry=109.0579858,-7.4095512&geometryType=esriGeometryPoint&outFields=*&f=pjson&where=
  let d = await axios
    .get(`https://gistaru.atrbpn.go.id/arcgis/rest/services/${mapObj.name}/query?geometry=${lon},${lat}&geometryType=esriGeometryPoint&outFields=*&f=pjson&where=`, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${gistaruData.token}`
      },
    })
    .catch((err) => {
      console.log(`gistaru: `+err);
      return null
    });
  if (!d?.data?.features?.length && kabObj) {
    d = await axios
      .get(`https://gistaru.atrbpn.go.id/arcgis/rest/services/${kabObj.name}/query?geometry=${lon},${lat}&geometryType=esriGeometryPoint&outFields=*&f=pjson&where=`, {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${gistaruData.token}`
        },
      })
      .catch((err) => {
        console.log(`gistaru: `+err);
        return null
      });
  }
  return d?.data
}

router.get('/', authMiddleware, async function(req, res) {
  const {lat, lon, kab, kec} = req.query
  let d = await getGistaru(lat, lon, kab, kec)
  res.send({
    success: true,
    data: d
  })
})

module.exports = router
