<template>
  <Page title="Laporan" :sidebar="true">
    <div style="background:white;">
      <div class="rowlnk">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'BCN_RptRekapKebencanaan',
              groupsheet: 'Kabupaten',
            })
          "
          >Data Bencana</v-btn
        >
      </div>
    </div>
    <div>
      <ReportParams
        v-show="showParams"
        :options="reportOptions"
        :generatedUrl.sync="reportUrl"
      />
      <ReportViewer :url="reportUrl" :show.sync="showReport" />
    </div>
  </Page>
</template>
<script>
import ReportParams from '../../components/Report/Params.vue'
import ReportViewer from '../../components/ReportViewer.vue'

export default {
  components: {
    ReportParams,
    ReportViewer,
  },
  data: () => ({
    reportOptions: null,
    reportUrl: 'about:blank',
    showReport: false,
    showParams: false,
  }),
  watch: {
    reportUrl(val) {
      if (val) this.showReport = true
    },
  },
  methods: {
    Generate(evt, opts) {
      this.showReport = false
      this.showParams = true
      opts.rptname = evt.target.innerText
      this.reportOptions = opts
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
</style>
