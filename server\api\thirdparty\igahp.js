const axios = require("axios");
const express = require('express')
const fs = require('fs')
const router = express.Router()
const FormData = require('form-data');
const {authMiddleware} = require('../auth')
const { wrapper } = require('axios-cookiejar-support')
const { <PERSON><PERSON>Jar } = require('tough-cookie')
const db = require('../../common/db')
const telegram = require("../../common/telegram");

const jar = new CookieJar()
const hq = wrapper(axios.create({
  baseURL: 'https://api.igahp.id',
  jar,
  withCredentials: true,
  timeout: 30000,
  headers: { 'User-Agent': 'Mozilla/5.0' },
}))

const login = async () => {
  hq.post('/api/auth/signin', {
    // "username": "<EMAIL>",
    // "password": "qazwsx"
    "username": "<EMAIL>",
    "password": "NUC.Es5:8UV.wsd"
  }).then(() => {
    console.log('IGAHP signin success')
  }).catch((err) => {
    let msg = err.response?.data || err.response || err.message
    console.log('(<EMAIL>) ' + msg)
    // telegram.sendAdmin('igahp/login (<EMAIL>): ' + JSON.stringify(msg))
  })
}

// const keepAlive = async () => {
//   hq.get('/api/igahp/getAllBanks').then(() => {
//     // console.log('IGAHP keepalive success')
//   }).catch(err => {
//     console.error(err.response?.data || err.response)
//   })
// }

const sleep = (ms) => new Promise((res) => setTimeout(res, ms))
const logError = (label, req, err) => {
  if(err.response?.data?.status === 404 || err.response?.data?.statusCode === 404) return
  if(err.response?.data?.status === 401) login()

  let msg = err.response?.data || err.response || err.message
  console.log(label + ': ' + JSON.stringify(msg || 'timeout'))
  if(msg && req?.app?.locals?.telegram) req.app.locals.telegram.sendAdmin(label + ': ' + JSON.stringify(msg || 'timeout'))
  return err.response?.data?.message || err.message ||err.response?.data || err.response || 'unknown error'
} 

login()
setInterval(login, 1000 * 60 * 14 * 1)

router.post('/login', async function(req, res) {
  let username = req.body.username.toLowerCase()
  let d = await axios.post('https://api.igahp.id/api/auth/signin', {
    "username": username,
    "password": req.body.password
  }).catch(err => { console.log(`(${username} : ${req.body.password})` + JSON.stringify(err.response?.data || err.response || err.message)) })

  if (d?.data?.token) {
    let u = d.data
    let roleId = 0
    if (u.roles.includes('ROLE_IGAHP_SURVEYOR')) {
      roleId = 26
    }

    let xs = await hq.get('/api/igahp/quesioner/listSurveyor').catch(async (err) => {
      console.log(`igahp/login (${username})`, err.response?.data || err.response || err.message)
    })
    let u2 = xs.data.find(ux => ux.email == u.email)
    if(u2) {
      await db.query(`INSERT IGNORE INTO arch_user (Username, Password, FullName, RolePositionID, Phone, IsActive) VALUES (?,MD5(?),?,?,?,?)
          ON DUPLICATE KEY UPDATE Password = VALUES(Password), Phone = VALUES(Phone)`, 
      [u.username, req.body.password, u2.namaLengkap.trim(), roleId, u2.id, 1]).catch(err => {
        console.log(err)
      })
    }
  }

  res.send({
    success: d ? true : false,
    message: d ? null : 'Login gagal',
    data: d?.data
  })
})

router.get('/get-surveyor-ids', async function(req, res) {
  let d = await hq.get('/api/igahp/quesioner/listSurveyor').catch(async (err) => {
    console.log(`igahp/get-surveyor-ids`, err.response?.data || err.response || err.message)
  })

  for (let o of d.data) {
    console.log(o.email, o.id)
    await db.query(`UPDATE arch_user SET Phone = ? WHERE Username = ?`, [o.id, o.email]).catch(err => {
      console.log(err)
    })
  }

  res.send({
    success: d ? true : false,
    message: d ? null : 'Login gagal',
    data: d?.data
  })
})

const getBase64 = async (url) => {
  if(!url) return null
  let fileUrl = url.replace(/^\//,'').replace(/\/ori\//,'/med/')
  if (process.env.NODE_ENV === 'development') {
    if (!fs.existsSync(fileUrl)) {
      const image = await axios.get('https://simperum.disperakim.jatengprov.go.id/' + fileUrl, {responseType: 'arraybuffer'});
      return Buffer.from(image.data).toString('base64');
    }
  }
  const b64 = fs.readFileSync(fileUrl, 'base64')
  // console.log(b64)
  return b64 
}

let inProgress = {}

const syncIGAHP = async (req, requestedAt) => {
  let d = []
  let retry = 0
  inProgress[req.body.NIK] = true
  let o = hq.get('/api/igahp/quesioner/nik?nik='+req.body.NIK, d[0]).catch(async (err) => {
    logError('igahp/get-nik', req, err)
  })
  do {
    d = await db.exec('OUT_SelLenteraHijau', {
      NIK: req.body.NIK,
    })
    if(!d.length) await sleep(2000)
    retry++
  }while(d.length == 0 && retry < 3)
  // console.log(d)
    
  let error = ''
  if (d.length) {
    o = await o
    // console.log(o.data?.data?.status)

    let status = o?.data?.data?.status || '0'

    // console.log(status)
    let xs = await axios.put('https://api.igahp.id/api/igahp/quesioner/updatePendataan/'+req.body.NIK, {
      ...d[0], 
      kodeWilayah: o?.data?.data?.kodewilayah?.kode || d[0].kodeWilayah,
      // statusData: status > 1 ? '2a' : status,
      statusData: status,
    },{
      headers: {
        Authorization: `Bearer ${req.body.accessToken}`
      }
    }).catch(async (err) => {
      error = logError(`igahp/sync (${req.body.NIK})`, req, err)
    })

    await axios.put('https://api.igahp.id/api/igahp/quesioner/updatePhotoByNik/'+req.body.NIK, {
      "photoDiri": await getBase64(d[0].Profile),
      "photoSekitar": await getBase64(d[0].RumahSamping),
      "photoRumah": await getBase64(d[0].RumahDepan),
      "photoLahan": await getBase64(d[0].Rumah0),
      "photoDiriLat": d[0].Profile ? d[0].ProfileLat || d[0].GeoLat: null,
      "photoDiriLon": d[0].Profile ? d[0].ProfileLon || d[0].GeoLng: null,
      "photoSekitarLat": d[0].RumahSamping ? d[0].RumahSampingLat || d[0].GeoLat: null,
      "photoSekitarLon": d[0].RumahSamping ? d[0].RumahSampingLon || d[0].GeoLng: null,
      "photoRumahLat": d[0].RumahDepan? d[0].RumahDepanLat || d[0].GeoLat: null,
      "photoRumahLon": d[0].RumahDepan? d[0].RumahDepanLon || d[0].GeoLng: null,
      "photoLahanLat": d[0].Rumah0 ? d[0].Rumah0Lat || d[0].GeoLat: null,
      "photoLahanLon": d[0].Rumah0 ? d[0].Rumah0Lon || d[0].GeoLng: null
    },{
      headers: {
        Authorization: `Bearer ${req.body.accessToken}`
      }
    }).catch(async (err) => {
      error = logError(`igahp/sync-foto (${req.body.NIK})`, req, err)
    })
    if(!error) {
      db.query('UPDATE iga_needsync SET syncedAt = ? WHERE NIK = ? AND RequestedAt = CAST(? AS DATETIME)', [new Date(), req.body.NIK, requestedAt])
    } else {
      console.log(error)
    }
  }
  delete inProgress[req.body.NIK]
  return error
}

router.post('/sync', authMiddleware, async function(req, res) {
  if (!req.body.accessToken) {
    res.send({
      success: false,
      message: 'Login IGAHP terlebih dahulu'
    })
    return
  }
  if (req.body.NIK) {
    let requestedAt = new Date()
    await db.query('INSERT INTO iga_needsync (NIK, Username, AccessToken, BodyJson, RequestedAt) VALUES (?,?,?,?,?)', [req.body.NIK, req.body.username, req.body.accessToken, JSON.stringify(req.body), requestedAt])
    syncIGAHP(req, requestedAt)

    res.send({
      success: true
    })
  } else {
    res.send({
      success: true
    })
  }
})

const sync2 = async (NIKs, req, headers = {}) => {
  for(let nik of NIKs) {
    console.log(nik)
    let d = []
    let retry = 0
    let o = hq.get('/api/igahp/quesioner/nik?nik='+nik, d[0]).catch(async (err) => {
      if(req) logError('igahp/get-nik', req, err)
    })
    do {
      d = await db.exec('OUT_SelLenteraHijau', {
        NIK: nik,
      })
      if(!d.length) await sleep(2000)
      retry++
    }while(d.length == 0 && retry < 3)
    // console.log(d)
  
    error = ''
    if (d.length) {
      o = await o

      let status = o?.data?.data?.status || '0'

      let xs = await hq.put('https://api.igahp.id/api/igahp/quesioner/updatePendataan/'+nik, {
        ...d[0], 
        kodeWilayah: d[0].kodeWilayah,
        // statusData: status > 1 ? '2a' : status,
        statusData: status,
      }, headers).catch(async (err) => {
        if(req) {
          error = logError('igahp/sync', req, err)
        }
      })

      // await axios.put('https://api.igahp.id/api/igahp/quesioner/updatePhotoByNik/'+nik, {
      //   "photoDiri": await getBase64(d[0].Profile),
      //   "photoSekitar": await getBase64(d[0].RumahSamping),
      //   "photoRumah": await getBase64(d[0].RumahDepan),
      //   "photoLahan": await getBase64(d[0].Rumah0),
      //   "photoDiriLat": d[0].Profile ? d[0].ProfileLat || d[0].GeoLat: null,
      //   "photoDiriLon": d[0].Profile ? d[0].ProfileLon || d[0].GeoLng: null,
      //   "photoSekitarLat": d[0].RumahSamping ? d[0].RumahSampingLat || d[0].GeoLat: null,
      //   "photoSekitarLon": d[0].RumahSamping ? d[0].RumahSampingLon || d[0].GeoLng: null,
      //   "photoRumahLat": d[0].RumahDepan? d[0].RumahDepanLat || d[0].GeoLat: null,
      //   "photoRumahLon": d[0].RumahDepan? d[0].RumahDepanLon || d[0].GeoLng: null,
      //   "photoLahanLat": d[0].Rumah0 ? d[0].Rumah0Lat || d[0].GeoLat: null,
      //   "photoLahanLon": d[0].Rumah0 ? d[0].Rumah0Lon || d[0].GeoLng: null
      // },{
      //   headers: {
      //     Authorization: `Bearer ${req.body.accessToken}`
      //   }
      // }).catch(async (err) => {
      //   error = logError('igahp/sync-foto', req, err)
      // })
      // if(!error) {
      //   db.query('UPDATE iga_needsync SET syncedAt = ? WHERE NIK = ? AND RequestedAt = CAST(? AS DATETIME)', [new Date(), req.body.NIK, requestedAt])
      // } else {
      //   console.log(error)
      // }
      if (!error) db.query('UPDATE iga_needsync SET syncedAt = ? WHERE NIK = ?', [new Date(), nik])
    }
  }
}

let error = ''
router.post('/sync2', async function(req, res) {
  if (req.body.NIK) {
    if (req.body.NIK.length == 1 && req.body.KodeDagri && req.body.Kabupaten) {
      await db.query(`update prm_pbdt SET 
        Kabupaten = '${req.body.Kabupaten}',
        Kecamatan = '${req.body.Kecamatan}',
        Kelurahan = '${req.body.Kelurahan}',
        KodeWilayah = '${req.body.KodeDagri}'
      where NIK IN (${req.body.NIK.join(',')})`)
    }

    let headers = {}
    if (req.body.accessToken) {
      headers = {
        headers: {
          Authorization: `Bearer ${req.body.accessToken}`
        }
      }
    }
    await sync2(req.body.NIK, req, headers)
    res.send({
      success: error || true
    })
  } else {
    res.send({
      success: error || true
    })
  }
})

router.post('/sync3', async function(req, res) {
  let dx = await db.query(`select * from iga_needsync 
    where SyncedAt is null AND RequestedAt > DATE_ADD(NOW(), INTERVAL -1 DAY)
    ORDER BY Username`)

  let user = null
  let NIKs = []
  let accessToken = null
  for(let x of dx) {
    if (user !== x.Username) {
      if(NIKs.length) {
        console.log(x.Username, NIKs)
        await sync2(NIKs, req, {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        })
      }
      NIKs = []
    }

    user = x.Username
    NIKs.push(x.NIK)
    accessToken = x.accessToken
  }
  res.send({
    success: true
  })
})

const resetStatus = async (req, id, nik, status = 0) => {
  var data = new FormData()
  data.append('_source', 'simperum')

  hq.put(`https://api.igahp.id/api/igahp/quesioner/updateStatus/${id}/${status}`, data).catch(err => {
    logError('igahp/resetStatus', req, err)
  })
  // await db.exec('IGA_UpdStatus', {NIK: nik, Status: status})
}

router.post('/check', authMiddleware, async function(req, res) {
  const {nik, nama} = req.body
  
  let d = await hq.get(`/api/igahp/quesioner/nik?nik=${nik}`)
    .catch(err => { 
      logError('igahp/check-search', req, err)
    })

  // console.log(d.data.data.status)
  // if(d?.data?.content?.length && d.data.content[0].namaKabkota == 'KAB SUKOHARJO') {
  if (d?.data?.data?.status == '0'){
    hq.post(`https://api.igahp.id/api/igahp/quesioner/RealTimeSubsidiChecking?nik=${nik}&nama=${nama}`, {
    }).catch(err => { 
      let msg = err.response?.data || err.response
      console.log(msg)
      if(msg?.error == 'Token Expired. Silahkan login kembali untuk mendapatkan token baru') {
        resetStatus(req, d.data.data.id, nik)
        login()
      } else if(msg && msg !== 'Tidak Lolos Subsidi Checking') {
        resetStatus(req, d.data.data.id, nik)
        logError(`igahp/check [${nik}]`, req, err)
      }
    })
  } else if (d?.data?.data?.status == '3') {
    resetStatus(req, d.data.data.id, nik, '2a')
  }
  
  res.send({
    success: true,
  })
})

router.post('/submit-all', async function (req, res) {
  let dx = await db.query(`select au.Username, ol.NIK from out_lenterahijau ol 
    left join arch_user au 
    on ol.UpdatedBy = au.UserID 
    where ol.NIK IN (3324071702610001,
3324070306860001,
3324072808920003,
3324070103800002,
3324071707870002,
3324076302550004)
    order by 1
    `)
  
  
  // let d = []
  let user = null
  for(let x of dx) {
    if (user !== x.Username) {
      await hq.post('/api/auth/signin', {
        "username": x.Username,
        "password": "igahpsurveyor"
      }).then(() => {
        user = x.Username
        console.log('IGAHP signin success')
      }).catch((err) => {
        let msg = err.response?.data || err.response
        console.log(msg)
      })
    }

    let d = []
    let retry = 0
    let o = await hq.get('/api/igahp/quesioner/nik?nik=' + x.NIK).catch(async (err) => {
      logError('igahp/get-nik', req, err)
    })

    let status = o?.data?.data?.status || '0'
    console.log(x.NIK, o?.data?.data?.id, status)

    if (status == '2d'){
      resetStatus(req, o.data.data.id, x.NIK, '3')
    } 
  }

  res.send({
    success: true
  })
})

router.get('/resubmit', async function(req, res) {
  let page= 0
  let d = null
  do {
    console.log(`page ${page}`)
    d = await hq.get(`/api/igahp/quesioner/allQuesioner-analisaSurvei?wilayah=3311&page=${page}&size=50&search=3311091612650001`)
    
    for(let o of d.data.content) {
      let p = await db.exec('OUT_SelLenteraHijau', {
        NIK: o.nik,
      })
      // console.log(p)
      let xs = await hq.put('/api/igahp/quesioner/updatePendataan/'+o.nik, {
        ...p[0], 
        statusData: o.status,
      }).catch(async (err) => {
        logError('igahp/sync', req, err)
      })
      await hq.put('/api/igahp/quesioner/updatePhotoByNik/'+req.body.NIK, {
        "photoDiri": await getBase64(p[0].Profile),
        "photoSekitar": await getBase64(p[0].RumahSamping),
        "photoRumah": await getBase64(p[0].RumahDepan),
        "photoLahan": await getBase64(p[0].Rumah0),
        "photoDiriLat": d[0].Profile ? d[0].ProfileLat || d[0].GeoLat: null,
        "photoDiriLon": d[0].Profile ? d[0].ProfileLon || d[0].GeoLng: null,
        "photoSekitarLat": d[0].RumahSamping ? d[0].RumahSampingLat || d[0].GeoLat: null,
        "photoSekitarLon": d[0].RumahSamping ? d[0].RumahSampingLon || d[0].GeoLng: null,
        "photoRumahLat": d[0].RumahDepan? d[0].RumahDepanLat || d[0].GeoLat: null,
        "photoRumahLon": d[0].RumahDepan? d[0].RumahDepanLon || d[0].GeoLng: null,
        "photoLahanLat": d[0].Rumah0 ? d[0].Rumah0Lat || d[0].GeoLat: null,
        "photoLahanLon": d[0].Rumah0 ? d[0].Rumah0Lon || d[0].GeoLng: null
      }).catch(async (err) => {
        logError('igahp/sync-foto', req, err)
      })
    }
    // let niks = d.data.content.map(d => d.nik)
    // //  console.log(niks.join(','))
    // await db.query(`
    //   UPDATE out_lenterahijau ol SET SubmittedAt = NOW()
    //   WHERE ol.SubmittedAt IS NULL AND NIK IN (${niks.join(',')})
    //   `)
    page++
  } while (d?.data?.content?.length > 0)
  res.send({
    success: true,
  })
})

const syncFoto = async(nik) => {
  let d = await db.exec('OUT_SelLenteraHijau', {
    NIK: nik,
  })
  const params = {
    "photoDiri": await getBase64(d[0].Profile),
    "photoSekitar": await getBase64(d[0].RumahSamping),
    "photoRumah": await getBase64(d[0].RumahDepan),
    "photoLahan": await getBase64(d[0].Rumah0),
    "photoDiriLat": d[0].Profile ? d[0].ProfileLat || d[0].GeoLat: null,
    "photoDiriLon": d[0].Profile ? d[0].ProfileLon || d[0].GeoLng: null,
    "photoSekitarLat": d[0].RumahSamping ? d[0].RumahSampingLat || d[0].GeoLat: null,
    "photoSekitarLon": d[0].RumahSamping ? d[0].RumahSampingLon || d[0].GeoLng: null,
    "photoRumahLat": d[0].RumahDepan? d[0].RumahDepanLat || d[0].GeoLat: null,
    "photoRumahLon": d[0].RumahDepan? d[0].RumahDepanLon || d[0].GeoLng: null,
    "photoLahanLat": d[0].Rumah0 ? d[0].Rumah0Lat || d[0].GeoLat: null,
    "photoLahanLon": d[0].Rumah0 ? d[0].Rumah0Lon || d[0].GeoLng: null
  }
  console.log(params)
  await hq.put('/api/igahp/quesioner/updatePhotoByNik/'+nik, params).catch(async (err) => {
    console.log(err.response?.data || err.response || err.message)
  })
}

router.get('/sync-foto', async function(req, res) {
  const nik = req.query.nik

  await syncFoto(nik)
  
  res.send({
    success: true,
  })  
})

let apiCache = {}
router.get('/list', authMiddleware, async function(req, res) {
  let qs = new URLSearchParams(req.query).toString() // .replace('&status=2a', '')
  // console.log(qs)

  let cacheQs = qs
  if (apiCache[cacheQs]) {
    // console.log('cache', cacheQs)
    let data = apiCache[cacheQs]
    res.send({
      success: true,
      data: {
        content: data
      }
    })
    return
  }
  try{
    let pdata = []
    let isError = false

    if(req.query.status == 0 || req.query.search || !req.query.status) {
      pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-dataAwal?${qs}`).catch(err => { isError = err }))
    }
    // if(qs.status == 1 || req.query.search || !req.query.status) {
    //   pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-dataAwal?${qs}`).catch(err => { isError = true }))
    // }
    if(req.query.status == '2a' || req.query.search || !req.query.status) {
      // let ts = 0
      // let t = {}
      // if (!req.query.search) {
      //   let t = await hq.get(`/api/igahp/quesioner/getDataSedangDieditTpp?${qs}`).catch(err => { isError = err })
      //   pdata.push(t)
      //   ts = t?.data?.content?.length
      // }
      // if(ts < 100) {
      //   console.log(req.query.page, t?.data?.totalPages)
      //   req.query.size = 100 - (ts % 100)
      //   req.query.page = req.query.page - (t?.data?.totalPages || 0)
      //   if (req.query.page < 0) req.query.page = 0
        
      //   qs = new URLSearchParams(req.query).toString()
      //   console.log(qs)
      //   pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-loloscheck?${qs}`).catch(err => { isError = err }))
      // }
      pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-loloscheck?${qs}`).catch(err => { isError = err }))
    }
    if(req.query.status == '2b' || req.query.search || !req.query.status) {
      pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-tidakLolosChecking?${qs}`).catch(err => { isError = err }))
    }
    if(req.query.status == 3 || req.query.status == 4 || req.query.search || !req.query.status) {
      pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-hasilSurvei?${qs}`).catch(err => { isError = err }))
    }
    // if(req.query.status == 4 || req.query.search || !req.query.status) {
    //   pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-hasilSurvei?${qs}`).catch(err => { isError = err }))
    // }
    if(req.query.status == 5 || req.query.search || !req.query.status) {
      pdata.push(hq.get(`/api/igahp/quesioner/allQuesioner-analisaSurvei?${qs}`).catch(err => { isError = err }))
    }

    // console.log(pdata)
    let data = (await Promise.all(pdata)).map(d => { return d?.data?.content || [] }).flat()
    // console.log(isError, data.length)
    /*let niks = data.map(d => d.nik)

    if (niks.length) {
      let perum = await db.query(`
        SELECT 
          NIK, Nama, ScoreTag, COALESCE(t.NamaData, 'Manual') NamaData, KepemilikanRumah, s.SumberName
        FROM prm_pbdt p
          LEFT JOIN prm_tipedata t
          ON p.TipeData = t.TipeData
          LEFT JOIN prm_sumber s
          ON p.Sumber =  s.SumberID
          AND s.SumberID NOT IN (11)
        WHERE NIK IN (${niks.join(',')})`)
      for(let d of data) {
        let prm = perum.find(p => p.NIK == d.nik)
        if(prm) {
          d.namaLengkap = prm.Nama
          d.scoreTag = prm.ScoreTag
          d.namaData = prm.NamaData
          d.kepemilikanRumah = prm.KepemilikanRumah
          d.sumberName = prm.SumberName
        }
      }
    }*/

    if (isError) console.log(isError)
    if(!isError) {
      if (data.length >= 100 && req.query.status == '2a' && !req.query.search) {
      // console.log('set cache', cacheQs)
        apiCache[cacheQs] = data
        setTimeout(() => {
          delete apiCache[cacheQs]
        }, 1000 * 60 * 15)
      } else if (!data?.length && req.query.search) {
        let perum = await db.query(`select 
            NIK nik, Nama namaLengkap, Alamat alamatDomisili, Kabupaten, Kecamatan, Kelurahan, KodeWilayah,
            'not_sent' status 
          FROM prm_pbdt pp 
          WHERE NIK = ${req.query.search}`)

        data = perum
      }
    }
    res.send({
      success: !isError,
      data: {
        content: data
      }
    })
  } catch(err) {
    res.send({
      success: true,
      data: {
        content: []
      }
    })
  }
})

router.get('/:p1/:p2', authMiddleware, async function(req, res) {
  const {p1, p2} = req.params
  const qs = new URLSearchParams(req.query).toString()
  // const qs = 'page=0&size=25&wilayah=&search='

  let d = await hq.get(`/api/igahp/${p1}/${p2}?${qs}`).catch(err => {
    logError(`igahp/${p1}/${p2}`, req, err)
  })
  // console.log(d)

  res.send({
    success: true,
    data: d?.data
  })
})

router.put('/:p1/:p2/:p3/:p4', authMiddleware, async function(req, res) {
  const {p1, p2, p3, p4} = req.params

  let retry = 0
  do {
    if(inProgress[req.body.NIK]) await sleep(2000)
    retry++
  }while(inProgress[req.body.NIK] && retry < 5)

  if(req.body.accessToken) {
    var data = new FormData()
    data.append('_source', 'simperum')
  
    let errMsg = null
    let d = await axios.put(`https://api.igahp.id/api/igahp/${p1}/${p2}/${p3}/${p4}`, data, {
      headers: {
        Authorization: `Bearer ${req.body.accessToken}`
      }
    }).catch(err => {
      errMsg = err.response?.data || err.response || err.message
      syncFoto(req.body.NIK)
      logError(`igahp/${p2}/${p3}/${p4}`, req, err)
    })
    if (errMsg === null) {
      if (p2 == 'updateStatus') {
        await db.exec('IGA_UpdStatus', {...req.body, Status: p4})
        if (req.body.KodeDagri) {
          let k = Object.keys(apiCache).find(qs => qs.match(req.body.KodeDagri))
          if (k) {
            let o = apiCache[k].find(d => d.nik == req.body.NIK)
            if (o) o.status = p4
          }
        }
      }
    }
    res.send({
      success: errMsg === null,
      data: d?.data || errMsg
    })
  } else {    
    res.send({
      success: false,
      message: 'Harap login IGAHP terlebih dahulu'
    })
  }
})

router.post('/check-sync', async function (req, res) {
  let userIds = await db.query(`select UserID, Username, Password, FullName, RolePositionID, Phone, Remarks from arch_user au 
  where RolePositionID = 26 and (Remarks LIKE '3374%' OR Remarks LIKE '3312%')
    `)
  for(let u of userIds) {
    const userId = u.UserID
    let perum = await db.query(`SELECT au.UserID, au.Username, au.Phone, ol.NIK FROM arch_user au
    LEFT JOIN out_lenterahijau ol 
    ON ol.UpdatedBy = au.UserID 
    AND ApprovalStatus = 'approved'
    WHERE au.UserID = ${userId}
    ORDER BY 1, 2
    `)
    const count = perum.filter(p => p.NIK != null).length
    console.log('check user', userId, perum[0].Username)

    let igahp = await hq.get('/api/igahp/quesioner/allQuesioner-analisaSurvei?page=0&size=500&wilayah=&search=&selectedSurveyor='+perum[0].Phone).catch(async (err) => {
      if (err.response?.data?.status === 401) login()
      logError('igahp/get-nik', req, err)
    })
    console.log(igahp.data.totalElements, ' vs ',count)
    if (igahp.data.totalElements > count) {
      for(let o of igahp.data.content) {
        let found = perum.find(p => p.NIK == o.nik)
        if (!found) {
          console.log('upd simperum ', o.nik, o.usernameEdit)
          if (o.status == '5') {
            await db.query(`UPDATE out_lenterahijau SET 
            ApprovalStatus = 'approved', ApprovedBy = 1, ApprovedAt = NOW(),
            UpdatedBy = COALESCE(UpdatedBy, ${perum[0].UserID}), SubmittedAt = COALESCE(SubmittedAt, NOW())
            WHERE NIK = ${o.nik}`)
          } else {
            console.log('status not 5: ', o.status)
          }
        }
      }
    } else if (count > igahp.data.totalElements){
      let founded = [] 
      await hq.post('/api/auth/signin', {
        "username": perum[0].Username,
        "password": "igahpsurveyor"
      }).then(() => {
        console.log('IGAHP signin success')
      }).catch((err) => {
        let msg = err.response?.data || err.response
        console.log(msg)
      })
      for(let p of perum) {
        let found = igahp.data.content.find(o => o.nik == p.NIK)
        if (!found) {
          let o = await hq.get('/api/igahp/quesioner/nik?nik=' + p.NIK).catch(async (err) => {
            if (err.response?.data?.status === 401) login()
            logError('igahp/get-nik', req, err)
          })          
          console.log('update igahp ', p.NIK, o.data.data.username.email)
          if (o.data.data.status < 5) {
            founded.push({NIK: p.NIK, id: o.data.data.id})
            await resetStatus(req, o.data.data.id, p.NIK, '3')
          } 
        }
      }
      await login()
      for(let p of founded) {
        await resetStatus(req, p.id, p.NIK, '5')
      }
    }
  }
  res.send({success: true})
})

module.exports = router
