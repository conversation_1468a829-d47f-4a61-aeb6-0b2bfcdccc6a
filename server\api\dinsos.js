
var express = require('express')
var utils = require('../common/utils')
var queries = require('./queries_select')
var db = require('../common/db')
var multer = require('multer')
var moment = require('moment')
var sharp = require('sharp')
var fs = require('fs')
var crypto = require('crypto')
var router = express.Router()
var dinsos = require('./thirdparty/dinsos')
const {authMiddleware} = require('./auth')

router.post('/get/:nik', authMiddleware, async function(req, res) {
  let d = await dinsos.getNIK(req.params.nik)
  res.send({
    success: d.NIK || d.nik ? true : false,
    data: d,
  })
})

const importKel = async (kab) => {
  let data = await dinsos.getByArea(kab.KodeDagri)
  let sqlinsert = 'INSERT IGNORE INTO prm_pbdt (NoRef, NIK, NoKK,  Nama, Alamat, IDART, Kabupaten, Kecamatan, Kelurahan, KodeWilayah, TipeData, Jenis<PERSON>, Tahun<PERSON>r, ModifiedDate)'
  if (data.length && data.forEach) {
    // console.log(`inserting ${kab.KodeDagri}: ${data.length} data`)
    sqlinsert += ' VALUES '
    data.forEach((d) => {
      // sqlinsert += `(
      //       ${'220000000000'.substring(0, 12-(''+d.id).length)}${d.id},
      //       ${d.NIK || 'NULL'},
      //       ${!d.NoKK || d.NoKK == '-' ? 'NULL': d.NoKK},
      //       '${d.Nama.replace(/'/g,'')}',
      //       '${(d.ALAMAT || '').replace(/'/g,'') || ''} RT ${d.RT || ''} RW ${d.RW || ''}',
      //       ${d.Hub_KRT > 1?d.Hub_KRT+'00'+d.id:'NULL'},
      //       '${kab.Kabupaten}',
      //       '${kab.Kecamatan}',
      //       '${kab.Kelurahan}',
      //       '${kab.KodeDagri}',
      //       3${moment().format('YY')},
      //       '${d.jns_kel == 'Laki-laki'?'L':'P'}',
      //       ${moment(d.tgl_lahir).format('YYYY')},
      //       NOW()
      //   ),`
      sqlinsert += `(
            ${'220000000000'.substring(0, 12-(''+d.id).length)}${d.id},
            ${d.nik || 'NULL'},
            ${!d.nokk || d.nokk == '-' ? 'NULL': d.nokk},
            '${d.nama.replace(/'/g,'')}',
            '${(d.alamat || '').replace(/'/g,'') || ''} RT ${d.rt || ''} RW ${d.rw || ''}',
            ${d.Hub_KRT > 1?d.Hub_KRT+'00'+d.id:'NULL'},
            '${kab.Kabupaten}',
            '${kab.Kecamatan}',
            '${kab.Kelurahan}',
            '${kab.KodeDagri}',
            3${moment().format('YY')},
            '${d.Jnskel == '1'?'L':'P'}',
            ${moment(d.Tgllahir).format('YYYY')},
            NOW()
        ),`
    })
    // console.log(sqlinsert)
    await db.query(sqlinsert.replace(/,$/, ';'))
    return {
      success: true,
      data: null,
      message: `${data.length} Data Imported Successfully`,
    }
  } else {
    return {
      success: false,
      data: 'Gagal mengambil data',
    }
  }
}

router.post('/import/:kdwil', authMiddleware, async function(req, res) {
  let kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
                WHERE KelurahanID = ${req.params.kdwil};`)
  if (kab.length) {
    res.send(await importKel(kab[0]))
  } else {
    res.send({
      success: false,
      data: 'Wilayah tidak terdaftar',
    })
  }
})



router.post('/import-kab/:kab', authMiddleware, async function(req, res) {
  let kabs = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av 
                  WHERE Kabupaten = '${req.params.kab}';`)
  if (kabs.length) {
    let r = {}
    for(let kab of kabs) {
      r = await importKel(kab)
      if(!r.success) {
        break;
      }
    }
    res.send(r)
  } else {
    res.send({
      success: false,
      data: 'Wilayah tidak terdaftar',
    })
  }
})

module.exports = router