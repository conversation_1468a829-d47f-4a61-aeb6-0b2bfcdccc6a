import Vue from 'vue'
import App from './App.vue'
import store from './store'
import router from './router'
import filters from './plugins/filters'
import componentDefault from './plugins/default'
import api from './api'
import Vuetify from 'vuetify'
import VTooltip from 'v-tooltip'
import VueToast from 'vue-toast-notification'
import { registerSW } from 'virtual:pwa-register'

registerSW({
  immediate: true,
  onNeedRefresh() {
    console.log('Need Refresh')
  },
  onOfflineReady() {
    console.log('Offline Ready')
  },
})

// import './assets/main.css'
import 'material-design-icons-iconfont/dist/material-design-icons.css'
import 'vue-toast-notification/dist/theme-sugar.css'

// Vue.config.productionTip = false

Vue.use(Vuetify)
Vue.use(VTooltip)
Vue.use(VueToast)
filters.register()
componentDefault.register()
Vue.prototype.$api = api

Vue.mixin({
  computed: {
    isMobile: () => {
      return window.innerWidth < window.innerHeight ? 'is-mobile' : ''
    },
    AllowWrite: () => {
      return Boolean(
        (window._rwx.match && window._rwx.match(/w/)) || window._rwx
      )
    },
    AllowDelete: () => {
      return Boolean(
        (window._rwx.match && window._rwx.match(/x/)) || window._rwx
      )
    },
  },
})

Vue.config.errorHandler = (err) => {
  console.error(err)

  if (window.location.hostname == 'localhost') return
  let popup = document.querySelector(
    '.v-dialog--active .v-card__title.headline'
  )
  if (popup) popup = ' (' + popup.innerHTML + ')'
  else popup = ''

  api.post('/api/log', {
    Source: 'FE',
    UserId: null,
    PageRef: window.location.href + popup,
    Details: err.stack,
  })
}

new Vue({
  render: (h) => h(App),
  vuetify: new Vuetify(),
  store,
  router,
}).$mount('#app')
