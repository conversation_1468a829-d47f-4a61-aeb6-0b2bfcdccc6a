<template>
  <div>
    <div>{{ label }}</div>
    <v-autocomplete
      v-model="vmodel"
      :items="itemlist"
      outlined
      dense
      chips
      small-chips
      :label="vplaceholder"
      multiple
    >
      <template v-slot:selection="data">
        <v-chip
          v-bind="data.attrs"
          :input-value="data.selected"
          @click="data.select"
          small
        >
          <v-icon small left :color="data.item.iconColor">
            {{ data.item.icon }}
          </v-icon>
          {{ data.item.textChip }}
        </v-chip>
      </template>
    </v-autocomplete>
  </div>
</template>
<script>
export default {
  data: () => ({
    allitems: [],
    itemlist: [],
  }),
  props: {
    label: String,
    placeholder: String,
    items: Array,
    dbref: String,
    dbparams: Object,
    value: [String, Object],
  },
  computed: {
    vmodel: {
      get() {
        return this.value ? this.value.split(',') : this.value
      },
      set(val) {
        this.$emit('update:value', val.join(','))
      },
    },
    vplaceholder() {
      return this.vmodel && this.vmodel.length
        ? this.placeholder
        : 'Tidak Ada ' + this.placeholder
    },
  },
  watch: {
    vmodel(val) {
      if (val && val.length) {
        let vx = []
        for (let v of val) {
          vx.push(v.split('-')[0])
        }
        this.itemlist = this.allitems.filter((item) => {
          return (
            val.includes(item.value) || !vx.includes(item.value.split('-')[0])
          )
        })
      }
    },
  },
  async created() {
    if (this.dbref) {
      let d = await this.$api.call(this.dbref, this.dbparams)
      this.allitems = [...d.data]
      this.itemlist = d.data
    } else {
      this.itemlist = this.items
    }
  },
}
</script>
