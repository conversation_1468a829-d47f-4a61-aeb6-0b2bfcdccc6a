<template>
  <Modal
    title="IGAHP Login"
    :show.sync="xshow"
    width="400px"
    :saving="saving"
    submitText="Login"
    @onSubmit="Save"
  >
    <div>
      <XInput label="Username" :value.sync="username" width="250px" />
      <XInput
        label="Password"
        type="password"
        :value.sync="password"
        width="250px"
      />
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    saving: false,
    username: '',
    password: '',
  }),
  props: {
    show: <PERSON><PERSON><PERSON>,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      if (!this.username || !this.password) {
        this.$api.notify('Lengkapi username dan password', 'error')
        return
      }
      this.saving = true
      let d = await this.$api.post('/api/igahp/login', {
        username: this.username,
        password: this.password,
      })
      this.saving = false
      if (d.success) {
        this.$emit('submit', {
          username: this.username,
          accessToken: d.data.accessToken,
        })
        this.$emit('update:show', false)
      } else {
        this.$api.notify(d.message, 'error')
      }
    },
  },
}
</script>
