{"name": "simperum", "version": "1.0.0", "description": "", "private": true, "workspaces": ["client", "server"], "scripts": {"start": "npm-run-all -l -p client server", "build": "yarn workspace client build", "deploy": "yarn workspace client build && deploy.bat", "client": "yarn workspace client denorun", "server": "yarn workspace server serve"}, "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"eslint-config-recommended": "^4.1.0"}}