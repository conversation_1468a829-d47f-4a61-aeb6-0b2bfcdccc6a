<template>
  <div style="display:inline-block;">
    <v-menu offset-y>
      <template v-slot:activator="{ on }">
        <v-btn v-bind="$attrs" v-on="on">
          <slot></slot>
          <v-icon right>expand_more</v-icon>
        </v-btn>
      </template>
      <v-list dense>
        <v-list-item
          v-for="(item, idx) in items"
          :key="idx"
          @click="item.click"
        >
          <v-list-item-icon v-if="item.icon">
            <v-icon>{{ item.icon }}</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title>{{ item.text }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>
<script>
export default {
  props: {
    items: Array
  }
};
</script>
