const jwt = require('jsonwebtoken')
JWT_SECRET = process.env.JWT_SECRET

const parseCookie = str =>
  str
    .split(';')
    .map(v => v.split('='))
    .reduce((acc, v) => {
      acc[decodeURIComponent(v[0].trim())] = decodeURIComponent(v[1].trim());
      return acc;
    }, {});

const getAuthToken = request => {
  // if (process.env.NODE_ENV == 'development') {
  //   const tokenHeader = request.headers.authorization
  //   if (tokenHeader && tokenHeader.startsWith('Bearer ')) {
  //     return tokenHeader.slice(7, tokenHeader.length)
  //   } else {
  //     // console.log(request.headers)
  //     return false
  //   }
  // } else {
  return request.headers.cookie ? parseCookie(request.headers.cookie)['twj'] : false
  // }
}

// Fastify authentication hook
const authHook = async (request, reply) => {
  let ip = (
    request.headers['cf-connecting-ip'] ||
    request.headers['x-real-ip'] ||
    request.headers['x-forwarded-for'] ||
    request.ip || ''
  )
  request._session = { ip }
  let token = getAuthToken(request)
  console.log(token)
  // Skip auth for EVO endpoints
  if (request.url.match(/^\/api\/call\/EVO/)) {
    return true
  }

  if (token) {
    try {
      const decoded = jwt.verify(token, JWT_SECRET)
      if (decoded) {
        // Ensure request.body exists
        if (!request.body) request.body = {}

        request.body._ip = ip
        request.body._userId = decoded.userId
        request.body._roleId = decoded.roleId
        request._session = decoded
        request._session.ip = ip
      }
    } catch (err) {
      // console.error(err.message)
      reply.send({
        success: false,
        message: 'Not authorized, Please Login Again.',
      })
      return
    }
  } else {
    reply.send({
      success: false,
      message: 'Not authorized, Please Login Again.',
    })
    return
  }
}

// Fastify plugin for authentication
const authPlugin = async (fastify, options) => {
  // Register cookie support
  await fastify.register(require('@fastify/cookie'), {
    secret: JWT_SECRET,
  })

  // Add authentication hook
  fastify.addHook('preHandler', authHook)

  // Decorate fastify instance with auth utilities
  fastify.decorate('getAuthToken', getAuthToken)
  fastify.decorate('jwtSignUser', jwtSignUser)
}

const jwtSignUser = (user) => {
  const ONE_DAY = 60 * 60 * 24 // * 7 * 4 * 4;
  return jwt.sign({ userId: user.UserID, roleId: user.RolePositionID }, JWT_SECRET, {
    expiresIn: ONE_DAY,
  })
}

module.exports = {
  getAuthToken,
  authHook,
  authPlugin,
  jwtSignUser
}