var mysql = require("mysql");
var util = require("util");
const fs = require("fs");
//const readline = require("readline");
var readlines = require("n-readlines");
const stream = require("stream");

var pool = mysql.createPool({
  connectionLimit: 10,
  host: "*************",
  user: "devusr",
  password: "Simperum2017!",
  database: "perum"
});

pool.getConnection((err, connection) => {
  if (err) {
    if (err.code === "PROTOCOL_CONNECTION_LOST") {
      console.error("Database connection was closed.");
    }
    if (err.code === "ER_CON_COUNT_ERROR") {
      console.error("Database has too many connections.");
    }
    if (err.code === "ECONNREFUSED") {
      console.error("Database connection was refused.");
    }
  }
  if (connection) connection.release();
  return;
});
pool.query = util.promisify(pool.query); // Magic happens here.

//3308
async function main() {
  var filename = `C:\\Users\\<USER>\\Downloads\\BDT 2019_1\\3329_ART.csv`;

  var liner = new readlines(filename);

  var sql = `INSERT IGNORE INTO raw_art
  (
    IDARTBDT
   ,IDBDT
   ,RUTA6
   ,NoPesertaPBDT
   ,KDPROP
   ,KDKAB
   ,KDKEC
   ,KDDESA
   ,NoPesertaPKH
   ,NoPesertaKKS2016
   ,NoPesertaPBI
   ,NoArtPBI
   ,Nama
   ,JnsKel
   ,TmpLahir
   ,TglLahir
   ,NIK
   ,NoKK
   ,Hub_KRT
   ,NUK
   ,Hubkel
   ,Umur
   ,Sta_kawin
   ,Ada_akta_nikah
   ,Ada_diKK
   ,Ada_kartu_identitas
   ,Sta_hamil
   ,Jenis_cacat
   ,Penyakit_kronis
   ,Partisipasi_sekolah
   ,Pendidikan_tertinggi
   ,Kelas_tertinggi
   ,Ijazah_tertinggi
   ,Sta_Bekerja
   ,Jumlah_jamkerja
   ,Lapangan_usaha
   ,Status_pekerjaan
   ,IDPENGURUS
   ,Alamat_Pengurus
   ,MasukKuota
   ,BSP_PengurusLainnya
   ,TipeData
  ) VALUES `;

  var i = 0;
  var next;
  var query = sql;
  while ((next = liner.next())) {
    var d = next.toString("ascii").split("|");
    if (d[0].trim().match(/^\d+$/)) {
      i++;
      query += "('" + d[0].replace(/[\\']/g, "") + "'";
      for (var j = 1; j < d.length; j++) {
        var val = d[j].trim().replace(/[\\']/g, "");
        //var val = d[j];
        if (val == "NULL" || !val || val == "-" || val == "NUL")
          query += ",NULL";
        else query += ",'" + val + "'";
      }
      query += ",119)";

      if (i % 500 == 0) {
        query += ";";
        await pool.query(query).catch(function(err) {
          console.error(err.sqlMessage);
          if (err.sqlMessage.indexOf("at line")) {
            var sqlpart = err.sqlMessage.substr(135, 80);
            var start = err.sql.indexOf(sqlpart);
            console.error(err.sql.substr(start - 30, 70));
            // var line = parseInt(err.sqlMessage.match(/at line (\d+)/)[1]);
            // var e = err.sql.split("),");
            // console.log(e[line - 1]);
            // console.log(e[line]);
            // console.log(e[line + 1]);
          }
          process.exit(0);
        });
        query = sql;
        console.log(`[!] inserted ${i}`);
      } else {
        query += ",";
      }
    }
  }

  console.log(`[!] FINAL ${i}`);
  if (i % 500 > 0) {
    query = query.substr(0, query.length - 1);
    query += ";";
    await pool.query(query).catch(function(err) {
      console.error(err.sqlMessage);
      process.exit(0);
    });
  }
  process.exit(0);
}

main();
