var mysql = require("mysql");
var util = require("util");

const DB_NAME = "test";

var pool = mysql.createPool({
  connectionLimit: 10,
  host: "************",
  user: "devusr",
  password: "Node50123!",
  // host: "localhost",
  // user: "root",
  // password: "qazwsx",
  database: DB_NAME
});

pool.query = util.promisify(pool.query); // Magic happens here.

async function build_query(sp_name, param) {
  var dbp = await pool.query(
    `SELECT * FROM information_schema.PARAMETERS 
    WHERE SPECIFIC_SCHEMA = '${DB_NAME}' AND SPECIFIC_NAME = '${sp_name}' AND PARAMETER_MODE = 'IN'`
  );

  var sql = "CALL " + sp_name + "(";
  var i = 0;
  var run_param = [];
  for (let idx in dbp) {
    var param_name = dbp[idx].PARAMETER_NAME; //.substr(1);
    var param_value = param[param_name] || param[param_name.replace(/^_/, "")];
    if (param_value) {
      if (param_name.substr(0, 4) == "_Xml") {
        //$sql += `<xml>.str_replace("'", "", str_replace("{", "<'", str_replace("}", ">'", $_VAR[$dbp['PARAMETER_NAME']]))).</xml>`;
        sql += "?";
        run_param.push(
          `<xml>${param_value.replace(/{/gi, "<").replace(/}/gi, ">")}</xml>`
        );
      } else if (param_value == "undefined") {
        sql += "?";
        run_param.push(null);
      } else if (param_value == "" && dbp[idx].DATA_TYPE == "int") {
        sql += "?";
        run_param.push(null);
      } else {
        sql += "?";
        run_param.push(param_value);
      }
    } else if (param_name == "_UserIDRef") {
      sql += "?";
      //run_param.push(1);
      run_param.push(param._userId);
    } else {
      sql += "?";
      run_param.push(null);
    }

    i++;
    if (i < dbp.length) sql += ",";
  }
  sql += ")";
  return {sql: sql, params: run_param};
}

module.exports = {
  exec: async function(sp, params) {
    sp = sp.replace(/[^\w_.]/gi, "");
    var query = await build_query(sp, params);
    // console.log(query.sql, query.params);
    var result = await pool.query(query.sql, query.params).catch(err => {
      console.error(`Error on API CALL: ${err.message}`);
      return [
        [{ErrCode: 100, Message: "Error on API Call", Error: err.message}]
      ];
    });
    return result;
  },
  getColumns: async function (table) {
    var result = await pool.query(`
      SELECT COUNT(1) FROM information_schema.COLUMNS c 
      WHERE TABLE_NAME = '${table}'`
    ).catch(err => {
      console.error(`Error on getColumns(${table}): ${err.message}`);
      return null
    });

    return result;
  },
  query: async function(sql) {
    var result = await pool.query(sql).catch(err => {
      console.error(`Error on Query: ${err.message}`);
      return [
        [{ErrCode: 100, Message: "Error on Query", Error: err.message}]
      ];
    });
    return result;
  },
  pool: pool,
  dbname: DB_NAME
};
