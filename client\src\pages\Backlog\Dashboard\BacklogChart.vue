<template>
  <div>
    <div style="height: calc(100vh - 360px)">
      <DoughnutChart
        :chart-data="datacollection"
        :options="chartOptions"
        class="yearly-chart"
      ></DoughnutChart>
    </div>
    <div
      id="chart-progress"
      style="
        width: 100%;
        height: 220px;
        background: white;
        box-sizing: border-box;
        padding: 15px;
        box-sizing: border-box;
        overflow-y: auto;
        font-size: 14px;
      "
    >
      <div style="font-size: small; margin-bottom: 5px">
        Total: <span class="data-total">{{ totalData | format }}</span>
      </div>
      <div style="margin-top: 7px" v-for="(item, idx) in progress" :key="idx">
        <v-icon left :color="colors[idx]">mdi-circle</v-icon>
        {{ item.KepemilikanDesc }}
        <span style="float: right" class="data-apbn">
          {{ item.Jml | format }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import DoughnutChart from '@/components/Charts/Doughnut.vue'

export default {
  components: {
    DoughnutChart,
  },
  data: () => ({
    progress: [],
    totalData: 0,
    colors: ['#77a7fb', '#e57368', '#fbcb43', '#34b67a', '#f1ab68'],
    datacollection: {},
    chartOptions: {
      title: {
        display: false,
        text: 'Chart',
      },
      legend: {
        display: false,
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
      },
      responsive: true,
      maintainAspectRatio: false,
    },
  }),
  created() {
    this.populateProgress()
    this.populateChart()
  },
  methods: {
    async populateProgress() {
      // let { data } = await this.$api.call("BLG.SelBacklogChart", {
      //   TipeData: 115
      // });
      // this.progress = data[0];
      // this.progress.TotalIntervensi =
      //   0 +
      //   data[0].APBN +
      //   data[0].APBD1 +
      //   data[0].APBD2 +
      //   data[0].CSR +
      //   data[0].Lain +
      //   data[0].Validasi +
      //   data[0].DiluarPrioritas;
      // this.progress.SisaPBDT =
      //   data[0].TotalData - this.progress.TotalIntervensi;
    },
    async populateChart() {
      let { data } = await this.$api.call('BLG.SelBacklogChart', {})
      this.progress = data
      let labels = []
      let ds = []
      data.forEach((d) => {
        labels.push(d.KepemilikanDesc)
        ds.push(d.Jml)
        this.totalData += d.Jml
      })
      this.datacollection = {
        labels: labels,
        datasets: [
          {
            data: ds,
            backgroundColor: this.colors,
          },
        ],
      }
    },
    randomScalingFactor() {
      return Math.floor(Math.random() * (50 - 5 + 1)) + 5
    },
  },
}
</script>
<style lang="scss">
.yearly-chart {
  canvas {
    height: 100% !important;
  }
}
</style>
