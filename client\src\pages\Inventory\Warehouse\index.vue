<template>
  <div style="padding:10px;">
    <Grid
      :datagrid="datatable"
      :columns="columns"
      :editable="true"
      style="width:500px"
      @dataUpdate="handleDataUpdate"
      @dataDelete="handleDataDelete"
    >
    </Grid>
  </div>
</template>

<script>
//import Vue from "vue";
import Input from "@/components/Forms/Input";
import Checkbox from "@/components/Forms/Checkbox";
import Grid from "@/components/Grid";
import "material-design-icons-iconfont/dist/material-design-icons.css";

export default {
  components: {
    Input,
    Checkbox,
    Grid,
  },
  data: () => ({
    text: "asd",
    files: [],
    datatable: null,
    columns: [
      {
        name: "Kode",
        value: "WarehouseCode",
        width: "80px",
        editable: {
          com: "Input",
        },
      },
      {
        name: "<PERSON><PERSON>",
        value: "WarehouseName",
        width: "180px",
        editable: {
          com: Input,
        },
      },
      {
        name: "<PERSON>tam<PERSON>?",
        value: "IsDefault",
        width: "70px",
        editable: {
          com: Checkbox,
        },
      },
      {
        name: "Stock?",
        value: "IsStock",
        width: "70px",
        editable: {
          com: Checkbox,
        },
      },
    ],
  }),
  created() {
    this.getWarehouse();
  },
  methods: {
    async getWarehouse() {
      let ret = await this.$api.select("warehouse");
      this.datatable = ret.data;
    },
    async handleDataUpdate(idx, data) {
      await this.$api.call("INV_SavWarehouse", data);
      // if (ret.success) this.datatable[idx] = data;
    },
    async handleDataDelete(idx) {
      let ret = await this.$api.call("INV_DelWarehouse", this.datatable[idx]);
      if (ret.success) this.datatable.splice(idx, 1);
    },
  },
};
</script>
