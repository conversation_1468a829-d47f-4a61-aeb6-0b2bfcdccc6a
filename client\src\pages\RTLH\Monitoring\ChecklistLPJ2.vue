<template>
  <Modal
    title="CHECKLIST LPJ"
    :show.sync="xshow"
    width="400px"
    @onSubmit="Save"
  >
    <div style="display: flex" class="form-inline">
      <div class="iblock" style="margin-right: 10px">
        <div
          style="
            padding: 8px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
          "
        >
          <div
            class="iblock"
            style="flex: 1; padding-left: 10px; font-size: small"
          >
            <i class="fa fa-file"></i>BERKAS LPJ
          </div>
        </div>
        <div style="display: flex">
          <!-- <Uploader
            :disabled="true"
            :value.sync="forms.LPJ"
            accept="application/pdf"
          ></Uploader> -->
          <div style="padding: 0 8px">
            <Checkbox
              text="Laporan Pelaksanaan"
              :value.sync="forms.LapPelaksanaan"
            />
            <Checkbox
              text="Surat Pernyataan Tanggung Jawab"
              :value.sync="forms.SuratTggJwb"
            />
            <Checkbox
              text="Laporan <PERSON>"
              :value.sync="forms.RealisasiDana"
            />
            <Checkbox
              text="Bukti Pengeluaran Uang"
              :value.sync="forms.BuktiUangKeluar"
            />
            <Checkbox
              text="Bukti Setoran Pajak"
              :value.sync="forms.BuktiPajak"
            />
            <Checkbox
              text="Dokumentasi Kegiatan"
              :value.sync="forms.FotoKegiatan"
            />
          </div>
        </div>
        <div
          style="
            padding: 8px 0;
            background: #f3f3f3;
            width: 100%;
            display: flex;
          "
        >
          <div
            class="iblock"
            style="flex: 1; padding-left: 10px; font-size: small"
          >
            <i class="fa fa-file"></i>BERKAS PADAT KARYA
          </div>
        </div>
        <div style="display: flex">
          <!-- <Uploader
            :disabled="true"
            :value.sync="forms.PadatKarya"
            accept="application/pdf"
          ></Uploader> -->
          <div style="padding: 0 8px">
            <Checkbox
              text="Laporan Pelaksanaan"
              :value.sync="forms.Pengantar"
            />
            <Checkbox
              text="Dokumentasi Pelaksanaan"
              :value.sync="forms.FCRekDesa"
            />
            <Checkbox
              text="Dok. Makan dan Minum"
              style="width: 300px"
              :value.sync="forms.DaftarHadir"
            />
            <!-- <Checkbox text="" :value.sync="forms.LapTPK" /> -->
            <Checkbox text="Kwitansi Upah" :value.sync="forms.BAPPencairan" />
          </div>
        </div>
      </div>
    </div>
    <template v-slot:left-action>
      <div style="padding-left: 12px; color: gray">
        <Checkbox text="Check Semua" :value.sync="checkAll" />
      </div>
    </template>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
    nik(val) {
      if (val) this.populate()
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x !== 'NIK') this.forms[x] = val
      }
    },
  },
  methods: {
    async populate() {
      this.checkAll = false
      let { data } = await this.$api.call('PRM.SelMonevCheck', {
        NIK: this.nik,
      })
      this.forms = data[0]
    },
    async Save() {
      let ret = await this.$api.call('PRM.SavMonevCheck', {
        ...this.forms,
        NIK: this.nik,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-proposal {
  .v-dialog--content {
    padding: 0;
  }
}
</style>
