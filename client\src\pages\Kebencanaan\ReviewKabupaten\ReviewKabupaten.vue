<template>
  <Page title="Review Kabupaten" :sidebar="true">
    <Sidebar
      dbref="BCN.SelReviewArea"
      :filter="{ IsComplete: 1 }"
      :value.sync="area"
      :tabs="[2]"
    >
      <template v-slot:badge="{ row }">
        <div class="badge s-jml" v-tooltip="'Jml Disetujui/Jml Usulan'">
          {{ row.JmlApprovalKab }} / {{ row.JmlProposal }}
        </div>
      </template>
    </Sidebar>
    <div style="padding: 10px 0 10px 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      />
      <!-- <v-btn
        v-if="!isApproved"
        color="primary"
        style="position: absolute;
          right: 8px;
          top: 107px;"
        @click="SavePengesahan(1)"
      >
        SETUJUI USULAN
      </v-btn>
      <v-btn
        v-else
        color="primary"
        text
        outlined
        style="position: absolute;
          right: 8px;
          top: 107px;"
      >
        SUDAH DISETUJUI
      </v-btn> -->
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :filter="filterRows"
        :disabled="true"
        height="calc(100vh - 230px)"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain',
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'Prior',
            value: 'Prioritas',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
          {
            name: '',
            value: 'HasMessage',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Dokumen',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Checklist',
            class: 'plain center',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox
            :value.sync="row.CheckedValue"
            checkedIcon="check_box"
            disabledIcon="mdi-lock"
            :disabled="Boolean(row.IsLocked)"
            @click="SubmitProposal(row.NIK, ...arguments)"
          />
        </template>
        <template v-slot:row-NIK="{ row }">
          <v-btn text small color="primary" @click="OpenDetail(row.NoRef)">
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
          >
            mdi-account-check
          </v-icon>
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-Dokumen="{ row }">
          <v-icon
            v-if="row.IsComplete"
            color="success"
            @click="OpenProposal(row.NIK)"
          >
            mdi-file-check
          </v-icon>
          <v-icon v-if="!row.IsComplete" @click="OpenProposal(row.NIK)">
            mdi-file-alert-outline
          </v-icon>
        </template>
        <template v-slot:row-Checklist="{ row }">
          <v-icon v-if="row.IsComplete" @click="OpenChecklist(row.NIK)">
            mdi-clipboard-list
          </v-icon>

          <v-btn
            v-if="row.CheckedValue"
            text
            small
            :color="row.KabKotaApproval ? 'success' : '#888'"
            @click="SavePengesahan(row, row.KabKotaApproval ? 0 : 1)"
          >
            <v-icon left v-if="row.KabKotaApproval">check_box</v-icon>
            <v-icon left v-if="!row.KabKotaApproval"
              >mdi-checkbox-blank-outline</v-icon
            >
            SETUJUI
          </v-btn>
        </template>
        <template v-slot:row-HasMessage="{ row }">
          <v-icon
            :color="row.HasMessage == 2 ? 'green' : 'silver'"
            v-tooltip="
              row.HasMessage == 2 ? 'Ada pesan baru' : 'tidak ada pesan baru'
            "
            @click="OpenMessages(row.NoRef)"
          >
            {{
              row.HasMessage ? 'mdi-message-text' : 'mdi-message-minus-outline'
            }}
          </v-icon>
        </template>
      </Grid>
      <ValidasiDetail
        :disabled="true"
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ProposalDetail
        :disabled="true"
        :nik="selectedNIK"
        :show.sync="showProposalModal"
      />
      <ChecklistProposal
        :disabled="true"
        :nik="selectedNIK"
        :show.sync="showChecklistModal"
      />
      <Messages
        :tahun="area.Tahun"
        :noRef="selectedRef"
        :show.sync="showMessages"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from '../InputUsulan/SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import ChecklistProposal from '../ReviewUsulan/Checklist.vue'
import Messages from '../ReviewUsulan/Messages.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ChecklistProposal,
    Messages,
  },
  data: () => ({
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showMessages: false,
    selectedRef: null,
    selectedNIK: null,
    isApproved: false,
  }),
  computed: {
    dbref() {
      return 'BCN.ProposalDet'
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue
      }).length
    },
  },
  watch: {
    area() {
      this.isApproved = false
    },
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
  },
  methods: {
    filterRows(d) {
      return d.CheckedValue == 1
    },
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenChecklist(nik) {
      this.selectedNIK = nik
      this.showChecklistModal = true
    },
    OpenMessages(noRef) {
      this.selectedRef = noRef
      this.showMessages = true
    },
    async SavePengesahan(row, appv) {
      let d = await this.$api.call('BCN.SavPengesahanKabupaten', {
        NIK: row.NIK,
        IsApproved: appv,
      })
      if (d.success) {
        row.KabKotaApproval = appv
      }
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
