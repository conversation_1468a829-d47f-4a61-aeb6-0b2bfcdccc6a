### Test Broadcast Notification (to all users)
POST http://localhost:8001/api/notifications/send
Content-Type: application/json

{
  "title": "Broadcast Test",
  "body": "This goes to all subscribed users",
  "url": "/"
}

### Test Targeted Notification (specific user)
POST http://localhost:8001/api/notifications/send
Content-Type: application/json

{
  "title": "Personal Notification",
  "body": "Only user123 will see this", 
  "url": "/profile",
  "userId": "user123"
}