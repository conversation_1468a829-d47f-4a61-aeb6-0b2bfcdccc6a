<template>
  <div>
    <v-row no-gutters>
      <v-col md="4" sm="12" style="padding: 0 20px 0 20px">
        <DoughnutChart
          :chart-data="dataRPJMD"
          :options="chartOptions1"
          :plugins="chartPlugins"
        ></DoughnutChart>
      </v-col>
      <v-col md="4" sm="12" style="padding: 0 20px 0 20px">
        <DoughnutChart
          :chart-data="dataTambahan"
          :options="chartOptions2"
          :plugins="chartPlugins"
        ></DoughnutChart>
      </v-col>
      <v-col md="4" sm="12" style="padding: 0 20px 0 20px">
        <DoughnutChart
          :chart-data="dataProgress"
          :options="chartOptions3"
          :plugins="chartPlugins"
        ></DoughnutChart>
      </v-col>
    </v-row>
  </div>
</template>
<script>
import DoughnutChart from '@/components/Charts/Doughnut.vue'

export default {
  components: {
    Doughnut<PERSON>hart,
  },
  data: () => ({
    progress: [],
    totalData: 0,
    colors: ['#77a7fb', '#e57368', '#fbcb43', '#34b67a', '#f1ab68'],
    dataRPJMD: {},
    dataTambahan: {},
    dataProgress: {},
    chartPlugins: [
      {
        afterDraw: (chart) => {
          var ctx = chart.chart.ctx
          var cw = chart.chart.canvas.offsetWidth
          var ch = chart.chart.canvas.offsetHeight
          var cx = cw / 2
          var cy = ch - (ch - cw / 2) / 2 + 10

          // subtitle
          if (chart.chart.options?.subtitle) {
            ctx.font = '14px sans-serif'
            const subtitle = chart.chart.options.subtitle.text
            const subWidth = ctx.measureText(subtitle).width

            ctx.fillText(subtitle, cx - subWidth / 2, 40)
          }

          // chart.chart.config.data.datasets.forEach((dataset) => {
          //   ctx.translate(cx, cy)
          //   const dataTotal = dataset.data.reduce((a, b) => a + b, 0)
          //   dataset.data.forEach(function (val) {
          //     const angle = Math.PI + (1 / dataTotal) * val * Math.PI
          //     ctx.rotate(angle)
          //     ctx.fillText(val, 0, 0)
          //   })
          // })

          if (!chart?.chart?.config?.data?.datasets?.length) return
          const needleValue = chart.chart.config.data.datasets[0].needleValue
          const dataTotal = chart.chart.config.data.datasets[0].data.reduce(
            (a, b) => a + b,
            0
          )
          const angle = Math.PI + (1 / dataTotal) * needleValue * Math.PI

          ctx.translate(cx, cy)
          ctx.rotate(angle)
          ctx.beginPath()
          ctx.moveTo(0, -3)
          ctx.lineTo(ch * 0.5, 0)
          ctx.lineTo(0, 3)
          ctx.fillStyle = 'rgb(0, 0, 0)'
          ctx.fill()
          ctx.rotate(-angle)
          ctx.translate(-cx, -cy)
          ctx.beginPath()
          ctx.arc(cx, cy, 5, 0, Math.PI * 2)
          ctx.fill()
        },
      },
    ],
    chartOptions1: {
      showAllTooltips: true,
      title: {
        display: true,
        text: 'RPJMD',
        fontSize: 20,
      },
      subtitle: {
        display: true,
        text: '(30 Juni 2023)',
      },
      legend: {
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
        position: 'bottom',
      },
      needle: {
        show: true,
      },
      rotation: Math.PI, // start angle in degrees
      circumference: Math.PI, // sweep angle in degrees
      maintainAspectRatio: false,
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            return (
              ' ' +
              data.labels[tooltipItem.index] +
              ': ' +
              data.datasets[0].data[tooltipItem.index].toLocaleString('id-ID')
            )
          },
          footer(tooltipItem, data) {
            let t = 0
            for (let val of data.datasets[0].data) {
              t += val
            }
            let x = t - data.datasets[0].data[2]
            return (
              'Total: ' +
              t.toLocaleString('id-ID') +
              '\nIntervensi: ' +
              x.toLocaleString('id-ID') +
              ' (' +
              ((x / t) * 100).toFixed(2) +
              '%)'
            )
          },
        },
      },
    },
    chartOptions2: {
      title: {
        display: true,
        text: 'Data Tambahan',
        fontSize: 20,
      },
      subtitle: {
        display: true,
        text: '(30 Juni 2023)',
      },
      legend: {
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
        position: 'bottom',
      },
      needle: {
        show: true,
      },
      rotation: Math.PI, // start angle in degrees
      circumference: Math.PI, // sweep angle in degrees
      maintainAspectRatio: false,
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            return (
              ' ' +
              data.labels[tooltipItem.index] +
              ': ' +
              data.datasets[0].data[tooltipItem.index].toLocaleString('id-ID')
            )
          },
          footer(tooltipItem, data) {
            let t = 0
            for (let val of data.datasets[0].data) {
              t += val
            }
            let x = t - data.datasets[0].data[1]
            return (
              'Total: ' +
              t.toLocaleString('id-ID') +
              '\nProgress: ' +
              x.toLocaleString('id-ID') +
              ' (' +
              ((x / t) * 100).toFixed(2) +
              '%)'
            )
          },
        },
      },
    },
    chartOptions3: {
      title: {
        display: true,
        text: 'Progress 2024',
        fontSize: 20,
      },
      legend: {
        labels: {
          usePointStyle: true,
          pointRadius: 2,
        },
        position: 'bottom',
      },
      needle: {
        show: true,
      },
      rotation: Math.PI, // start angle in degrees
      circumference: Math.PI, // sweep angle in degrees
      maintainAspectRatio: false,
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            return (
              ' ' +
              data.labels[tooltipItem.index] +
              ': ' +
              data.datasets[0].data[tooltipItem.index].toLocaleString('id-ID')
            )
          },
          footer(tooltipItem, data) {
            // let tt = ''
            // for (let label of meta.labels) {
            //   tt += label + ': '
            // }
            let t = 0
            for (let val of data.datasets[0].data) {
              t += val
            }
            console.log(data.datasets[0])
            let x = t - data.datasets[0].data[4]
            return (
              'Total: ' +
              t.toLocaleString('id-ID') +
              '\nProgress: ' +
              x.toLocaleString('id-ID') +
              ' (' +
              ((x / t) * 100).toFixed(2) +
              '%)'
            )
          },
        },
      },
    },
  }),
  created() {
    this.populateChart()
  },
  methods: {
    async populateChart() {
      let { data } = await this.$api.call('PRM.SelChartQuarterlyTotal', {
        nocache: true,
      })
      // this.progress = data
      // let labels = []
      // let ds = []
      // data.forEach((d) => {
      //   labels.push(d.KepemilikanDesc)
      //   ds.push(d.Jml)
      //   this.totalData += d.Jml
      // })
      this.dataProgress = {
        labels: ['Q1', 'Q2', 'Q3', 'Q4', 'Sisa'],
        datasets: [
          {
            // label: '# of Votes',
            data: [
              data[0]['Q1'],
              data[0]['Q2'],
              data[0]['Q3'],
              data[0]['Q4'],
              data[0]['Sisa'],
            ],
            needleValue:
              data[0]['Q1'] + data[0]['Q2'] + data[0]['Q3'] + data[0]['Q4'],
            backgroundColor: [
              '#58508d',
              '#bc5090',
              '#ff6361',
              '#ffa600',
              '#ddd',
            ],
          },
        ],
      }
      this.dataRPJMD = {
        labels: ['Verivali', 'Penanganan', 'Sisa RPJMD'],
        datasets: [
          {
            // label: '# of Votes',
            data: [1052516, 100699, 529508],
            needleValue: 100699 + 1052516,
            backgroundColor: ['#276db9', '#563b8d', '#ddd'],
          },
        ],
      }
      this.dataTambahan = {
        labels: ['Penanganan', 'Sisa'],
        datasets: [
          {
            // label: '# of Votes',
            data: [494204, 482954],
            needleValue: 494204,
            backgroundColor: ['#fe9f1b', '#ddd'],
          },
        ],
      }
    },
    randomScalingFactor() {
      return Math.floor(Math.random() * (50 - 5 + 1)) + 5
    },
  },
}
</script>
<style lang="scss">
.yearly-chart {
  canvas {
    height: 100% !important;
  }
}
</style>
