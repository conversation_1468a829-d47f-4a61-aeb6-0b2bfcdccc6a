var express = require("express");
var multer = require("multer");
var moment = require("moment");
var sharp = require("sharp");
var fs = require("fs");
var router = express.Router();
const WebSocket = require("ws");

// const ws = new WebSocket("ws://localhost:8289");

// ws.on("open", function open() {
//   ws.send("something");
// });

var upload = multer({dest: "uploads/"});
const uploadfunction = function(req, res) {
  if (req.file.originalname.match(/[jpg|jpeg]/)) {
    var tmp_path = req.file.path;

    var ext = ".jpg";
    if (req.file.mimetype == "image/jpeg") ext = ".jpg";
    else ext = "." + req.file.originalname.substr(-3);

    var year_dir = moment().format("YYWW");
    var filename = moment().format("DDHHmmssSSS") + ext;
    var target_path = "uploads/" + year_dir + "/ori/";
    if (!fs.existsSync("uploads/" + year_dir)) {
      fs.mkdirSync("uploads/" + year_dir);
      fs.mkdirSync("uploads/" + year_dir + "/ori/");
      fs.mkdirSync("uploads/" + year_dir + "/med/");
      fs.mkdirSync("uploads/" + year_dir + "/small/");
      fs.mkdirSync("uploads/" + year_dir + "/tiny/");
    }

    var src = fs.createReadStream(tmp_path);
    var dest = fs.createWriteStream(target_path + filename);
    src.pipe(dest);
    src.on("end", async function() {
      await sharp(tmp_path)
        .resize(600)
        .toFile(target_path.replace(/\/ori\//, "/med/") + filename)
        .catch(err => console.log(err));
      await sharp(target_path + filename)
        .resize(250)
        .toFile(target_path.replace(/\/ori\//, "/small/") + filename)
        .catch(err => console.log(err));
      await sharp(target_path + filename)
        .resize(80)
        .toFile(target_path.replace(/\/ori\//, "/tiny/") + filename)
        .catch(err => console.log(err));
      //res.render("complete");
      res.send({
        Success: true,
        Data: "/" + target_path + filename,
        Message: "File Uploaded!",
        Type: "string"
      });
    });
    src.on("error", function(err) {
      res.send({
        Success: false,
        Data: err.message,
        Message: err.message,
        Type: "error"
      });
    });
  } else {
    res.send({
      Success: false,
      Data: "mimetype not supported",
      Message: "mimetype not supported",
      Type: "error"
    });
  }
};

// APP EXTENDER
router.get("/Main/App/Extender", function(req, res) {
  // console.log(req.query);
  wss.clients.forEach(function each(client) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(req.query));
    }
  });
  res.send({
    success: true,
    name: "SIMPERUM",
    features: "Barcode|Upload"
  });
});
router.post("/Main/App/Extender", function(req, res) {
  //console.log(wsServer.send({ test: 123 }));
  res.send({
    success: true,
    name: "SIMPERUM",
    features: "Barcode|Upload"
  });
});
router.post("/Main/App/Extender/Upload", upload.single("file"), uploadfunction);
module.exports = router;
