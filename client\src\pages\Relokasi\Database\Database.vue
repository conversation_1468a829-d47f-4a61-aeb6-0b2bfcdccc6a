<template>
  <Page title="Database Relokasi">
    <template v-slot:toolbar>
      <!-- <v-menu offset-y>
        <template v-slot:activator="{ on }">
          <v-btn small color="primary" v-on="on">IMPORT</v-btn>
        </template>
        <v-list dense style="width: 250px">
          <v-list-item @click="OpenImportExcel">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-excel</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Dari Excel</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu> -->
      <!-- <v-icon @click="doPrint++">print</v-icon> -->
    </template>
    <Grid
      id="table-db-blg"
      :datagrid.sync="backlog"
      dbref="RLK.Database"
      :dbparams="filters"
      :disabled="true"
      :height="'calc(100vh - 120px)'"
      :doRebind="pbdtRebind"
      :columns="[
        {
          name: 'NIK',
          value: 'NIK',
          class: 'plain',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: '',
          value: 'VerStatsID',
          class: 'plain center',
        },
        {
          name: 'Nama',
          value: 'KRT_Nama',
          width: '200px',
          class: 'fix-width',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: 'No. KK',
          value: 'NoKK',
          hide: true,
          filter: {
            type: 'search',
            value: 'NoKK',
          },
        },
        {
          name: 'IDBDT',
          value: 'NoRef',
          hide: true,
        },
        {
          name: 'Alamat',
          value: 'Alamat',
          width: '250px',
          class: 'fix-width',
        },
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          filter: {
            type: 'select',
            value: 'KabupatenID',
            text: 'Kabupaten',
            dbref: 'Arch.SelArea',
            dbparams: { ParentAreaID: 33 },
          },
        },
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          filter: {
            type: 'select',
            value: 'KecamatanID',
            text: 'Kecamatan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KabupatenID }),
          },
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          filter: {
            type: 'select',
            value: 'KelurahanID',
            text: 'Kelurahan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KecamatanID }),
          },
        },
        {
          name: 'Lahan',
          value: 'StatusTanah',
        },
        {
          name: 'Luas',
          value: 'LuasTanah',
        },
        {
          name: 'DT',
          value: 'NamaData',
        },
        {
          name: 'Intervensi',
          value: 'Intervensi',
        },
      ]"
      :doPrint="doPrint"
      @onPrint="Print"
    >
      <template v-slot:pre-head="{ allfilters, openColumnSetting }">
        <Panel dbref="RLK.SelDescDatabase" :dbparams="allfilters">
          <template v-slot="{ first }">
            <div style="display: flex" class="pre-head">
              <div style="padding: 8px 12px; flex: 2; font-size: 14px">
                <XInput
                  rightIcon="search"
                  width="230px"
                  placeholder="Cari NIK/Nama .."
                  :value.sync="keyword"
                />
              </div>
              <div class="table-desc" style="">
                <v-btn text small color="gray" @click="openColumnSetting">
                  <v-icon left>mdi-cog</v-icon>
                  ATUR KOLOM
                </v-btn>
                <div style="background-color: #95a5a6">
                  Total: {{ first.Total | format }}
                </div>
                <div style="background-color: #2ecc71">
                  Intervensi {{ first.Intervensi | format }}
                </div>
                <div style="background-color: #ecf0f1; color: gray">
                  Sisa: {{ first.Sisa | format }}
                </div>
              </div>
            </div>
          </template>
        </Panel>
      </template>
      <template v-slot:row-NIK="{ row }">
        <v-btn text small color="primary" @click="OpenDetail(row.NoRef)">
          {{ row.NIK || '&lt; kosong &gt;' }}
        </v-btn>
      </template>
      <template v-slot:row-VerStatsID="{ row }">
        <v-icon
          v-if="row.VerStatsID >= 6"
          color="primary"
          v-tooltip="'Sudah Terverifikasi'"
          >mdi-account-check</v-icon
        >
        <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
          mdi-account-question-outline
        </v-icon>
      </template>
    </Grid>
    <ValidasiDetail
      :show.sync="showDetailModal"
      :noRef="selectedRef"
      :disabled="true"
    />
    <ImportExcel :show.sync="showImportExcel" />
    <ReportViewer :options="reportOptions" :show.sync="showReport" />
  </Page>
</template>
<script>
import ReportViewer from '../../../components/ReportViewer.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ImportExcel from './ImportExcel.vue'

export default {
  components: {
    ValidasiDetail,
    ReportViewer,
    ImportExcel,
  },
  data: () => ({
    showDetailModal: false,
    showImportExcel: false,
    filters: {},
    selectedRef: null,
    backlog: [],
    reportUrl: null,
    showReport: false,
    doPrint: 0,
    reportOptions: {},
    keyword: '',
    pbdtRebind: 1,
  }),
  async mounted() {},
  watch: {
    keyword(val) {
      this.filters.Keyword = val
      this.pbdtRebind++
    },
  },
  methods: {
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenImportExcel() {
      this.showImportExcel = true
    },
    Print(headers, data) {
      this.reportOptions = {
        headers: headers,
        data: data,
      }
      this.showReport = true
    },
  },
}
</script>
<style lang="scss">
.table-desc {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;

  div {
    padding: 3px 5px;
    margin-left: 3px;
    color: white;
  }
}
</style>
