<template>
  <div>
    <Grid
      :datagrid.sync="roles"
      dbref="Arch.RolePosition"
      :dbparams="dbparams"
      :disabled="true"
      style="height:300px"
      class="dense"
      :columns="[
        {
          name: 'Menu',
          value: 'RolePositionName',
        },
        {
          name: 'Home Url',
          value: 'HomeUrl',
        },
        {
          name: 'Parent',
          value: 'ParentName',
        },
        {
          name: 'Admin?',
          value: 'IsAdmin',
          editable: {
            com: 'Checkbox',
          },
        },
      ]"
    >
      <template v-slot:row-IsAdmin="{ row }">
        <div center>
          <v-icon>
            {{
              row.IsAdmin ? 'mdi-checkbox-marked-outline' : 'mdi-crop-square'
            }}
          </v-icon>
        </div>
      </template>
    </Grid>
  </div>
</template>
<script>
export default {
  data: () => ({
    roles: null,
    forms: {},
  }),
  props: {
    roleId: [String, Number],
    'page-data': Array,
  },
  async mounted() {},
  watch: {
    roles(val) {
      this.$emit('update:page-data', val)
    },
  },
  computed: {
    dbparams() {
      return {
        RolePositionID: this.roleId,
      }
    },
  },
  methods: {
    ShowRoleAccess() {
      this.roleAccess = true
    },
  },
}
</script>
