const webpush = require('web-push');
const express = require('express');
const router = express.Router();

// Configure VAPID keys
webpush.setVapidDetails(
  'mailto:<EMAIL>',
  'BHNlq0xbaervKOcZHEFR8lNa8Z1F72uB5lsbZf9NyLtI7AGF2d9nwNPiFSrNqkwDneL8UpBsZKcJ7pQeNKTXUE0',
  'm5wI5LzqXAaDb9GBYTcYKpXI1Hes1olo2doxmiCRDkQ'
);

// In-memory store for subscriptions (in production, use a database)
const subscriptions = new Map();

// Endpoint to save subscription
router.post('/subscribe', (req, res) => {
  const { subscription, userId } = req.body;
  if (!subscription || !userId) {
    return res.status(400).send('Subscription and userId required');
  }

  const subId = subscription.keys.auth;
  subscriptions.set(subId, { subscription, userId });
  res.status(201).send('Subscription saved');
});

// Endpoint to remove subscription
router.post('/unsubscribe', (req, res) => {
  const subscription = req.body;
  if (!subscription) {
    return res.status(400).send('Subscription required');
  }

  const id = subscription.keys.auth;
  subscriptions.delete(id);
  res.status(200).send('Subscription removed');
});

// Helper function to send notification
function sendNotification(subscription, payload) {
  return webpush.sendNotification(subscription, JSON.stringify(payload))
    .catch(err => {
      if (err.statusCode === 410) {
        // Subscription no longer valid - remove it
        const id = subscription.keys.auth;
        subscriptions.delete(id);
      }
      console.error('Error sending notification:', err);
    });
}

// Endpoint to send notification (for testing)
router.post('/send', (req, res) => {
  const { title, body, userId } = req.body;
  if (!title || !body) {
    return res.status(400).send('Title and body required');
  }

  const payload = {
    title,
    body,
    url: '/',
    userId
  };

  // Send to all subscriptions
  const promises = Array.from(subscriptions.values())
    .map(sub => sendNotification(sub, payload));

  Promise.all(promises)
    .then(() => res.send('Notifications sent'))
    .catch(err => res.status(500).send('Error sending notifications'));
});

module.exports = router;